--!strict

--[[
    - file: MAFS_SERVER.LUAU

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Server-side implementation for the MAFS (Material Audio Footstep System).
      - Handles player movement detection, material resolution, and sound broadcasting.
      - Integrates with performance monitoring and centralized configuration.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils
      - MAFS_Remotes
      - MAFS_Configuration

    - notes:
      - Manages footstep detection and broadcasting to all clients.
      - Uses centralized configuration and proper logging throughout.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")
local Config = require(
  ReplicatedStorage:WaitForChild("Configurations")
    :WaitForChild("Systems")
    :WaitForChild("MAFS_Configuration")
)
local Remotes = require(mainPath:WaitForChild("MAFS_Remotes"))
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local STEP_INTERVAL_BASE = 0.4
local SPEED_FACTOR_MIN = 0.5
local SPEED_FACTOR_MAX = 2.0
local SPEED_DIVISOR = 10

-- ============================================================================
-- TYPES
-- ============================================================================
export type MAFSConfiguration = Types.MAFSConfiguration
export type PlayerFootstepState = Types.PlayerFootstepState

-- ============================================================================
-- VARIABLES
-- ============================================================================
local footstepSoundsPlaying: { [Player]: boolean } = {}
local lastMaterial: { [Player]: Enum.Material } = {}
local lastFootprintTime: { [Player]: number } = {}
local performanceMonitor: Types.PerformanceMonitor? = nil

task.spawn(function()
  while true do
    task.wait(0.1)
    local now = os.clock()

    for _, player in Players:GetPlayers() do
      local character = player.Character
      if not character then
        continue
      end

      local humanoid = character:FindFirstChildOfClass("Humanoid")
      local root = character:FindFirstChild("HumanoidRootPart") :: BasePart?
      if not humanoid or not root then
        continue
      end

      local velocity = root.AssemblyLinearVelocity.Magnitude
      local moveDirMag = humanoid.MoveDirection.Magnitude
      local material = humanoid.FloorMaterial

      local isMoving = velocity > 1 and moveDirMag > 0 and material ~= Enum.Material.Air
      local soundName = Config.FootstepSoundMap[material] or "Grass"

      -- Start / stop / update looped footstep sound
      local prevMaterial = lastMaterial[player]

      if isMoving then
        lastMaterial[player] = material

        if not footstepSoundsPlaying[player] then
          footstepSoundsPlaying[player] = true
          Remotes.StartFootstepSound:FireClient(player, soundName)
        elseif prevMaterial and prevMaterial ~= material then
          Remotes.UpdateFootstepSound:FireClient(player, soundName)
        end
      else
        if footstepSoundsPlaying[player] then
          footstepSoundsPlaying[player] = nil
          Remotes.StopFootstepSound:FireClient(player)
        end
        lastMaterial[player] = nil
        lastFootprintTime[player] = nil
      end

      -- Replicated footprints
      if isMoving then
        local lastTime = lastFootprintTime[player] or 0
        local speedFactor = math.clamp(velocity / 10, 0.5, 2)
        local interval = STEP_INTERVAL_BASE / speedFactor

        if now - lastTime >= interval then
          lastFootprintTime[player] = now
          local footprintName = Config.FootprintMaterials[material]
          if footprintName then
            for _, other in Players:GetPlayers() do
              local otherChar = other.Character
              local otherRoot = (
                otherChar and otherChar:FindFirstChild("HumanoidRootPart")
              ) :: BasePart?
              if
                otherRoot and (otherRoot.Position - root.Position).Magnitude <= Config.SoundRadius
              then
                Remotes.ReplicateFootprint:FireClient(
                  other,
                  player.Name,
                  root.Position,
                  footprintName
                )
              end
            end
          end
        end
      end

      -- Replicated 3D sound
      if isMoving then
        for _, other in Players:GetPlayers() do
          if other == player then
            continue
          end
          local otherChar = other.Character
          local otherRoot = (otherChar and otherChar:FindFirstChild("HumanoidRootPart")) :: BasePart?
          if otherRoot and (otherRoot.Position - root.Position).Magnitude <= Config.SoundRadius then
            Remotes.PlayFootstep:FireClient(other, root.Position, soundName)
          end
        end
      end
    end
  end
end)
