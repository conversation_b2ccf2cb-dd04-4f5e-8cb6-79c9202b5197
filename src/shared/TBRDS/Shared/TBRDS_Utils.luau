-- --!strict

--[[
    - file: TBRDS_Utils.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Utility module for managing events in TBRDS
      - Provides event subscription and publishing
--    - Supports both server and client events
--    - Handles event security and performance monitoring
--    - Designed for use with TBRDS systems
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================

local PerformanceMonitor = require(script.Parent.TBRDS_PerformanceMonitor)
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Parent.TBRDS_Types)

-- ============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- ============================================================================

-- Type Aliases
local TBRDSConstants = Types.TBRDSConstants

-- Type Definitions (from TBRDS_Types)
type RateLimiter = Types.RateLimiter -- This is not a error
type ValidationResult = Types.ValidationResult -- This is not a error
type RoleStyle = Types.RoleStyle -- This is not a error

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================

local Utils = {}

-- ============================================================================
-- DEBUG FUNCTIONS
-- ============================================================================

-- Check if Debug Mode is enabled
function Utils.isDebugMode()
  return TBRDSConfig.Settings.DebugMode
end

-- Debug print function with module identifier
function Utils.print(moduleName: string, message: string)
  if Utils.isDebugMode() then
    print(string.format("[TBRDS:%s]: %s", moduleName, message))
  end
end

-- Debug logging with performance tracking
function Utils.debugLog(moduleName: string, message: string, trackPerformance: boolean?)
  if Utils.isDebugMode() then
    local timestamp = os.time()
    local formattedMessage = string.format("[TBRDS:%s][%d]: %s", moduleName, timestamp, message)
    print(formattedMessage)

    if trackPerformance then
      PerformanceMonitor.RecordError("DEBUG_LOG")
    end
  end
end

-- ============================================================================
-- RATE LIMITER
-- ============================================================================

-- Create an rate limiter with security features
function Utils.createRateLimiter(window: number?, limit: number?): RateLimiter
  local actualWindow = window or TBRDSConfig.Settings.RateLimit.Window
  local actualLimit = limit or TBRDSConfig.Settings.RateLimit.MaxRequests

  local limiter = {
    Window = actualWindow,
    Limit = actualLimit,
    Requests = {}, -- {[userId] = {timestamps}}
  }

  -- Check if a request is allowed
  function limiter:CheckLimit(userId: number): (boolean, number)
    -- Initialize if needed
    self.Requests[userId] = self.Requests[userId]
      or {
        Requests = {},
        LastReset = os.time(),
      }

    local userRequests = self.Requests[userId]
    local now = os.time()

    -- Remove old timestamps
    local validTimestamps = {}
    for _, timestamp in ipairs(userRequests.Requests) do
      if now - timestamp < self.Window then
        table.insert(validTimestamps, timestamp)
      end
    end

    -- Update timestamps
    userRequests.Requests = validTimestamps

    -- Check if under limit
    local count = #userRequests.Requests
    if count < self.Limit then
      -- Add current timestamp
      table.insert(userRequests.Requests, now)
      PerformanceMonitor.RecordCacheHit() -- Rate limit allowed
      return true, count + 1
    else
      -- Over limit - record security event
      PerformanceMonitor.RecordSecurityEvent(TBRDSConstants.ERROR_CODES.RATE_LIMIT_EXCEEDED)
      return false, count
    end
  end

  -- Reset rate limit for a user
  function limiter:Reset(userId: number): ()
    if self.Requests[userId] then
      self.Requests[userId] = {
        Requests = {},
        LastReset = os.time(),
      }
    end
  end

  return limiter
end

-- =======================================================================
-- INSTANCE UTILITY FUNCTIONS
-- =======================================================================

-- Function to find a descendant by name (case-insensitive)
function Utils.findDescendantIgnoreCase(parent: Instance, name: string): Instance?
  -- Make the string lower (to be case-insensitive)
  name = string.lower(name)

  -- For each child that matches the string, return it
  for _, child in ipairs(parent:GetDescendants()) do
    -- Make the child a game object with a name
    if
      typeof(child) == "Instance" and child:IsA("BasePart")
      or child:IsA("GuiObject")
      or child:IsA("Model")
    then
      if string.lower(child.Name) == name then
        return child -- Return the child with a name
      end
    end
  end
  return nil
end

-- Function to create a simple text label
function Utils.createTextLabel(
  parent,
  name,
  text,
  textColor,
  font,
  textSize,
  backgroundTransparency
)
  local label = Instance.new("TextLabel")
  label.Name = name
  label.Parent = parent
  label.Text = text
  label.TextColor3 = textColor or Color3.new(1, 1, 1)
  label.Font = font or Enum.Font.SourceSans
  label.TextSize = textSize or 12
  label.BackgroundTransparency = backgroundTransparency or 1
  label.TextScaled = false
  label.TextSize = 24
  return label
end

-- Function to create a simple image label
function Utils.createImageLabel(parent, name, image, size, position, backgroundTransparency)
  local imageLabel = Instance.new("ImageLabel")
  imageLabel.Name = name
  imageLabel.Parent = parent
  imageLabel.Image = image
  imageLabel.Size = size or UDim2.new(1, 0, 1, 0)
  imageLabel.Position = position or UDim2.new(0, 0, 0, 0)
  imageLabel.BackgroundTransparency = backgroundTransparency or 1
  return imageLabel
end

-- Billboard creation with configuration support
function Utils.createTagBillboard(player: Player, roleName: string, style: RoleStyle): BillboardGui?
  if not player.Character then
    return nil
  end

  local head = player.Character:FindFirstChild("Head")
  if not head then
    return nil
  end

  -- Remove existing billboard if present
  local existingBillboard = head:FindFirstChild("TagGui")
  if existingBillboard then
    existingBillboard:Destroy()
  end

  local settings = TBRDSConfig.Settings.BillboardSettings

  -- Create new billboard
  local billboard = Instance.new("BillboardGui")
  billboard.Name = "TagGui"
  billboard.Adornee = head
  billboard.Size = settings.Size
  billboard.StudsOffset = settings.StudsOffset
  billboard.AlwaysOnTop = settings.AlwaysOnTop
  billboard.MaxDistance = settings.MaxDistance
  billboard.Active = true
  billboard.ZIndexBehavior = Enum.ZIndexBehavior.Global
  billboard.LightInfluence = settings.LightInfluence
  billboard.Parent = head

  -- Create a frame to hold the tag elements
  local frame = Instance.new("Frame")
  frame.Name = "TagFrame"
  frame.Size = UDim2.new(1, 0, 1, 0)
  frame.BackgroundTransparency = 1
  frame.Parent = billboard

  -- Add UIListLayout for horizontal arrangement
  local layout = Instance.new("UIListLayout")
  layout.FillDirection = Enum.FillDirection.Horizontal
  layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
  layout.VerticalAlignment = Enum.VerticalAlignment.Center
  layout.SortOrder = Enum.SortOrder.LayoutOrder
  layout.Padding = UDim.new(0, 10)
  layout.Parent = frame

  -- Add icon if specified
  if style.Image then
    local icon = Instance.new("ImageLabel")
    icon.Name = "Icon"
    icon.Size = UDim2.new(0, 30, 0, 30)
    icon.BackgroundTransparency = 1
    icon.Image = style.Image
    icon.LayoutOrder = 1
    icon.ImageTransparency = 0
    icon.ScaleType = Enum.ScaleType.Fit
    icon.AnchorPoint = Vector2.new(0.5, 0.5)
    icon.Position = UDim2.new(0.5, 0, 0.5, 0)
    icon.Parent = frame
  end

  -- Create the tag label
  local label = Instance.new("TextLabel")
  label.Name = "TagLabel"
  label.Size = UDim2.new(0, 150, 0, 30)
  label.BackgroundTransparency = 1
  label.Text = style.GetText and style.GetText(player) or "[" .. roleName .. "]"
  label.TextColor3 = style.Color
  label.Font = style.Font
  label.TextScaled = true
  label.TextStrokeTransparency = 0.5
  label.LayoutOrder = 2
  label.Parent = frame

  -- Apply gradient if specified
  if style.Gradient then
    local gradient = Instance.new("UIGradient")
    if #style.Gradient.Colors == 2 then
      local keypoints = {
        ColorSequenceKeypoint.new(0, style.Gradient.Colors[1]),
        ColorSequenceKeypoint.new(1, style.Gradient.Colors[2]),
      }
      gradient.Color = ColorSequence.new(keypoints)
    else
      local keypoints = {}
      for i, color in ipairs(style.Gradient.Colors) do
        local position = (i - 1) / (#style.Gradient.Colors - 1)
        table.insert(keypoints, ColorSequenceKeypoint.new(position, color))
      end
      gradient.Color = ColorSequence.new(keypoints)
    end
    gradient.Rotation = style.Gradient.Rotation
    gradient.Parent = label
  end

  -- Apply text effects if specified
  if style.TextStroke then
    label.TextStrokeColor3 = style.TextStroke.Color
    label.TextStrokeTransparency = style.TextStroke.Transparency
  end

  return billboard
end

-- ============================================================================
-- SECURITY & VALIDATION FUNCTIONS
-- ============================================================================

-- Validate player data
function Utils.validatePlayer(player: Player): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }

  -- Check if player exists
  if not player or not player:IsA("Player") then
    result.ErrorCode = TBRDSConfig.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Invalid player object" -- This is not a error
    return result
  end

  -- Check if player is still in game
  if not player.Parent then
    result.ErrorCode = TBRDSConfig.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Player no longer in game" -- This is not a error
    return result
  end

  -- Check if player has valid UserId
  if not player.UserId or player.UserId <= 0 then
    result.ErrorCode = TBRDSConfig.ErrorCodes.INVALID_PLAYER
    result.ErrorMessage = "Invalid player UserId" -- This is not a error
    return result
  end

  result.Success = true
  return result
end

-- Validate role name
function Utils.validateRole(roleName: string): boolean
  if not roleName or type(roleName) ~= "string" then
    return false
  end

  if #roleName > TBRDSConfig.Settings.MaxTagLength then
    return false
  end

  -- Check if role exists in priority list
  for _, validRole in ipairs(TBRDSConfig.RolePriority) do
    if validRole == roleName then
      return true
    end
  end

  return false
end

-- Table to track recent role changes per player (module-level, not exported)
local roleChangeHistory = {}

-- Security validation
function Utils.validateSecurity(player: Player, _context: string): { string }?
  local flags = {}

  -- Check for rapid role changes (simple anti-abuse: >3 changes in 10 seconds)
  local now = os.time()
  local userId = player and player.UserId or nil
  if userId then
    roleChangeHistory[userId] = roleChangeHistory[userId] or {}
    local history = roleChangeHistory[userId]
    -- Remove old entries (older than 10 seconds)
    local newHistory = {}
    for _, t in ipairs(history) do
      if now - t < 10 then
        table.insert(newHistory, t)
      end
    end
    table.insert(newHistory, now)
    roleChangeHistory[userId] = newHistory
    if #newHistory > 3 then
      table.insert(flags, "RAPID_ROLE_CHANGE")
    end
  end

  -- Check for suspicious patterns
  -- This would include more sophisticated checks

  if #flags > 0 then
    PerformanceMonitor.RecordSecurityEvent("SECURITY_VALIDATION_FAILED")
    return flags
  end

  return nil
end

-- ======================================================================
-- EXPORTS
-- ======================================================================
return Utils
