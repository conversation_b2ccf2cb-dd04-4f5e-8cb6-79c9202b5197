--!strict

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local Types = require(ReplicatedStorage.Mechanics.Ragdoll.Ragdoll_Types)

local RagdollConfig: Types.RagdollConfig = {

  -- Debug
  Debug = true,

  -- Default max ragdolls
  MaxRagdolls = 15,

  -- Default lifetime in seconds before cleanup
  MaxRagdollLifetime = 90,

  -- Default force for ragdoll physics
  PhysicsForce = 100,

  -- Ragdolls collide with environment
  CollisionEnabled = true,
}

return RagdollConfig
