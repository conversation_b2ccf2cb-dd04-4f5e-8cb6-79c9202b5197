--!strict

--[[
    - file: Configurationluau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized configuration for the Modular Command System (MCS)
      - All system-wide settings, constants, and configuration tables
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Types = require(ReplicatedStorage.MCS.MCS_Types)

-- ============================================================================
-- DEFINE LUAU MODULE
-- ============================================================================

local MCSConfig: {
  Settings: Types.SettingsType,
  Group: Types.ConfigType,
  Commands: Types.CommandsType,
  UI_Constants: Types.UI_ConstantsType?,
  Middleware: Types.Middleware?,
  [string]: any,
} =
  {
    Settings = {} :: Types.SettingsType,
    Group = {} :: Types.ConfigType,
    Commands = {} :: Types.CommandsType,
    UI_Constants = {} :: Types.UI_ConstantsType,
    Middleware = {} :: Types.Middleware,
  }

-- ============================================================================
-- SYSTEM SETTINGS
-- ============================================================================

MCSConfig.Settings = {
  DebugMode = true,
  EnablePerformanceMetrics = true,
  AutocompleteCacheDuration = 60, -- 60 seconds
} :: Types.SettingsType

-- ============================================================================
-- GROUP & PERMISSION CONFIGURATION
-- ============================================================================

MCSConfig.Group = {
  -- Group ID
  GROUP_ID = 34320208,

  -- Rank to Permission level
  RANK_TO_LEVEL = {
    [255] = 6, -- Founder & CEO
    [254] = 5, -- Co-Founder
    [253] = 4, -- Senior Moderator
    [252] = 3, -- Moderator
    [251] = 2, -- Junior Moderator
    [1] = 1, -- Member
  },

  -- Special users (ID)
  SPECIAL_USERS = {},

  -- Cache Duration
  CACHE_DURATION = 300, -- seconds
} :: Types.ConfigType

-- ============================================================================
-- SYSTEM COMMANDS CONSTANTS
-- ============================================================================

MCSConfig.Commands = {
  -- Command prefix, client/server parsing
  PREFIX = "!",

  -- Maximum length of command text
  MAX_COMMAND_LENGTH = 86,

  -- Maximum number of arguments allowed
  MAX_ARGS = 10,

  -- Maximum number of suggestions allowed
  MAX_SUGGESTIONS = 8,

  -- Commands that accept player arguments (for autocomplete)
  PLAYER_ARGUMENT_COMMANDS = {
    "ban",
    "kick",
    "grantperm",
    "revokeperm",
    "unban",
    "mute",
    "unmute",
  },
} :: Types.CommandsType

-- ============================================================================
-- UI CONSTANTS
-- ============================================================================

MCSConfig.UI_Constants = {
  -- Dark background
  BACKGROUND = Color3.fromRGB(30, 30, 35),

  -- Teal highlight
  BORDER = Color3.fromRGB(240, 240, 230),

  -- Bright green
  SUCCESS_TEXT = Color3.fromRGB(80, 250, 123),

  -- Bright red
  ERROR_TEXT = Color3.fromRGB(255, 85, 85),

  -- Off-white
  TEXT = Color3.fromRGB(248, 248, 242),

  -- Rounded corners
  CORNER_RADIUS = UDim.new(0, 6),

  -- Animation Time
  ANIMATION_TIME = 0.3,

  -- Max height of suggestions
  MAX_SUGGESTION_HEIGHT = 150,

  -- Suggestion button height
  SUGGESTION_BUTTON_HEIGHT = 28,

  -- Suggestion padding
  SUGGESTION_PADDING = 2,
} :: Types.UI_ConstantsType

-- ============================================================================
-- MIDDLEWARE CONSTANTS
-- ============================================================================

MCSConfig.Middleware = {
  Initialization = {
    MAX_MIDDLEWARE_EXECUTION_TIME = 3, -- 3 seconds
    MAX_RETRIES = 3, -- 3 retry attempts
    MIDDLEWARE_TIMEOUT = 2, -- 2 seconds per middleware of timeout
  },
  RateLimiter = {
    CLEANUP_INTERVAL = 60, -- 60 seconds
    CLEANUP_PROBABILITY = 0.1, -- 10% chance per command to trigger cleanup
    HISTORY_RETENTION_TIME = 300, -- 5 minutes of command history retention
  },
  Logger = {
    CONSOLE_LOGGING = true,
    DATASTORE_LOGGING = false,
    WEBHOOK_LOGGING = false,
    WEBHOOK_URL = nil,
    DATASTORE_NAME = "MCS_Logger",
    MAX_LOG_ENTRIES = 1000,
    SENSITIVE_COMMANDS = {
      ["ban"] = true,
      ["kick"] = true,
      ["warn"] = true,
    },
    LOG_RETENTION_DAYS = 30,
  },
  Analytics = {
    ANALYTICS_DATASTORE_NAME = "MCS_Analytics",
    PLAYER_ANALYTICS_DATASTORE_NAME = "MCS_PlayerAnalytics",
    SAVE_INTERVAL = 300, -- 5 Minutes
    MAX_RETRIES = 3,
    RETRY_DELAY = 5,
  },
} :: Types.Middleware

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MCSConfig
