--!strict

--[[
    - file: CGS_Configuration.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized configuration for the Core Game System (CGS).
      - Organized into modular sections for easier maintenance and use.
      - Includes runtime validation and utility functions for safe access.

    - dependencies:
      - CGS_Types

    - notes:
      - Utility Functions must not be moved to CGS_Utils and CGS_Utils shall not be required because:
       - (Type Error: Cyclic module dependency) will appear in Script Analysis if you do so.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- PATHS
-- ============================================================================
local mainPath = ReplicatedStorage.CGS

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(mainPath:WaitForChild("CGS_Types"))

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

--[[
    Validates a number value, ensuring it is within optional min/max bounds.

    @param value The number to validate
    @param min Optional minimum value (inclusive)
    @param max Optional maximum value (inclusive)
    @param name The name of the value for error reporting

    @return The validated number, clamped to min/max if provided

    @error If value is not a number
]]
local function validateNumber(value: number, min: number?, max: number?, name: string): number
  assert(type(value) == "number", `{name} must be a number`)
  if min and value < min then
    warn(`{name} ({value}) is below minimum ({min}), clamping`)
    return min
  end
  if max and value > max then
    warn(`{name} ({value}) exceeds maximum ({max}), clamping`)
    return max
  end
  return value
end

--[[
    Validates a Vector3 value, ensuring it is of the correct type.

    @param value The Vector3 to validate
    @param name The name of the value for error reporting

    @return The validated Vector3

    @error If value is not a Vector3
	]]
local function validateVector3(value: Vector3, name: string): Vector3
  assert(typeof(value) == "Vector3", `{name} must be a Vector3`)
  return value
end

-- ============================================================================
-- MAIN CONFIGURATION
-- ============================================================================
local CGS_Config: Types.Configuration = {

  -- General Settings
  General = {
    DebugMode = false,
    PerformanceMonitor = false,
  },

  -- Movement Settings
  Movement = {
    -- Base speeds (studs/second)
    IdleSpeed = validateNumber(0, 0, 100, "MovementIdleSpeed"),
    WalkSpeed = validateNumber(13, 0, 100, "MovementWalkSpeed"),
    SprintSpeed = validateNumber(24, 0, 100, "MovementSprintSpeed"),
    CrouchSpeed = validateNumber(8, 0, 100, "MovementCrouchSpeed"),
    ProneSpeed = validateNumber(4, 0, 10, "MovementProneSpeed"),

    -- Directional modifiers
    StrafeSpeedMultiplier = validateNumber(0.45, 0, 1, "MovementStrafeSpeedMultiplier"),
    BackwardSpeedMultiplier = validateNumber(0.25, 0, 1, "MovementBackwardSpeedMultiplier"),

    -- Physics
    Acceleration = validateNumber(50, 0, 100, "MovementAcceleration"),
    Deceleration = validateNumber(70, 0, 100, "MovementDeceleration"),

    -- Stamina system
    MaxStamina = validateNumber(100, 0, 200, "MovementMaxStamina"),
    StaminaRegenRate = validateNumber(5, 0, 20, "MovementStaminaRegenRate"),
    SprintStaminaDrain = validateNumber(2, 0, 100, "MovementSprintStaminaDrain"),
    JumpStaminaCost = validateNumber(20, 0, 100, "MovementJumpStaminaCost"),

    -- Jump mechanics
    JumpPower = validateNumber(15, 0, 20, "MovementJumpPower"),
    JumpCooldown = validateNumber(1, 0, 5, "MovementJumpCooldown"),

    -- State transitions
    TransitionSpeed = validateNumber(8, 4, 10, "MovementTransitionSpeed"),

    -- Gamepad
    Gamepad_Deadzone = validateNumber(0.1, 0, 10, "MovementGamepadDeadZone"),

    -- Exhaustion
    DepletionWindow = validateNumber(10, 1, 100, "MovementDepletionWindow"),
    MaxDepletions = validateNumber(3, 0, 10, "MovementMaxDepletions"),
    ExhaustDuration = validateNumber(5, 0, 15, "MovementExhaustDuration"),
    RegenMultiplierExhausted = validateNumber(0.5, 0, 5, "MovementRegenMultiplerExhausted"),
  },

  -- Observation Settings
  Observation = {

    BodyView = {
      Enabled = true,
      Priority = validateNumber(1, 0, 10, "BodyViewPriority"),
    },

    Bobbing = {
      Enabled = true,
      Priority = validateNumber(2, 0, 10, "BobbingPriority"),
      MovementThreshold = validateNumber(0.075, 0, 10, "BobbingMovementThreshold"),
    },

    Sway = {
      Enabled = true,
      Priority = validateNumber(3, 0, 10, "SwayPriority"),

      -- Mouse Sway (Head Tilt Left/Right)
      HeadTurnAmount = validateNumber(0.05, 0, 0.1, "SwayTurnAmount"),
      TurnSpeed = validateNumber(6, 0, 20, "SwayTurnSpeed"),
      TurnClamp = validateNumber(6, 0, 45, "SwayTurnClamp"),

      -- Idle Sway (Breathing Effect)
      IdleAmount = validateVector3(Vector3.new(0.02, 0.02, 0), "IdleSwayAmount"),
      IdleSpeed = validateNumber(0.4, 0, 10, "IdleSwaySpeed"),
      IdleThreshold = validateNumber(4, 0, 10, "IdleThreshold"),
    },

    DynamicFOV = {
      Enabled = true,
      Priority = validateNumber(4, 0, 10, "FOVPriority"),
      DefaultFOV = validateNumber(70, 60, 90, "DynamicFOV.DefaultFOV"),
      SprintFOV = validateNumber(85, 80, 100, "DynamicFOV.SprintFOV"),
      JumpFOV = validateNumber(75, 60, 90, "DynamicFOV.JumpFOV"),
      FreeFallFOV = validateNumber(90, 80, 100, "DynamicFOV.FreeFallFOV"),
      SwimFOV = validateNumber(65, 60, 90, "DynamicFOV.SwimFOV"),
      CrouchFOV = validateNumber(65, 60, 90, "DynamicFOV.CrouchFOV"),
      ProneFOV = validateNumber(60, 60, 90, "DynamicFOV.ProneFOV"),
      TransitionSpeed = validateNumber(8, 1, 20, "DynamicFOV.TransitionSpeed"),
    },

    MotionBlur = {
      Enabled = true,
      Priority = validateNumber(5, 0, 10, "MotionBlurPriority"),
      maxBlur = validateNumber(20, 1, 20, "MotionBlurMax"),
      rotationSensitivity = validateNumber(0.1, 0.1, 1, "MotionBlurSensitivity"),
      speedThreshold = validateNumber(15, 0, 20, "MotionBlurSpeedThreshold"),
      FadeSpeed = validateNumber(5, 0.1, 10, "MotionBlurFadeSpeed"),
    },

    DirectionalHead = {
      Enabled = true,
      Priority = validateNumber(6, 0, 10, "DirectionalHeadPriority"),
      ActivationDistance = validateNumber(50, 10, 100, "DirectionalHeadActivationDistance"),
    },

    UnderwaterEffect = {
      Enabled = true,
      Priority = validateNumber(7, 0, 10, "UnderwaterPriority"),
    },

    IgnoredAccessories = {
      ["CoolShades"] = true,
      ["Backpack"] = true,
    },

    IntensityMultiplier = 5,

    ShakePresets = {

      Explosion = {
        intensity = validateNumber(2.5, 0, 10, "Explosion.intensity"),
        frequency = validateNumber(15, 1, 50, "Explosion.frequency"),
        duration = validateNumber(1.2, 0, 5, "Explosion.duration"),
        fadeIn = validateNumber(0.1, 0, 1, "Explosion.fadeIn"),
        fadeOut = validateNumber(0.8, 0, 2, "Explosion.fadeOut"),
        direction = validateVector3(Vector3.new(1, 1, 0.3), "Explosion.direction"),
        damping = validateNumber(0.95, 0, 1, "Explosion.damping"),
        roughness = validateNumber(0.8, 0, 1, "Explosion.roughness"),
      },

      Earthquake = {
        intensity = validateNumber(1.8, 0, 10, "Earthquake.intensity"),
        frequency = validateNumber(8, 1, 50, "Earthquake.frequency"),
        duration = validateNumber(3.0, 0, 5, "Earthquake.duration"),
        fadeIn = validateNumber(0.3, 0, 1, "Earthquake.fadeIn"),
        fadeOut = validateNumber(1.5, 0, 2, "Earthquake.fadeOut"),
        direction = validateVector3(Vector3.new(0.2, 1, 0.2), "Earthquake.direction"),
        damping = validateNumber(0.98, 0, 1, "Earthquake.damping"),
        roughness = validateNumber(0.6, 0, 1, "Earthquake.roughness"),
      },

      Impact = {
        intensity = validateNumber(3.0, 0, 10, "Impact.intensity"),
        frequency = validateNumber(25, 1, 50, "Impact.frequency"),
        duration = validateNumber(0.4, 0, 5, "Impact.duration"),
        fadeIn = validateNumber(0.05, 0, 1, "Impact.fadeIn"),
        fadeOut = validateNumber(0.3, 0, 2, "Impact.fadeOut"),
        direction = validateVector3(Vector3.new(0.8, 0.8, 0.1), "Impact.direction"),
        damping = validateNumber(0.92, 0, 1, "Impact.damping"),
        roughness = validateNumber(0.9, 0, 1, "Impact.roughness"),
      },

      Subtle = {
        intensity = validateNumber(0.5, 0, 10, "Subtle.intensity"),
        frequency = validateNumber(12, 1, 50, "Subtle.frequency"),
        duration = validateNumber(2.0, 0, 5, "Subtle.duration"),
        fadeIn = validateNumber(0.5, 0, 1, "Subtle.fadeIn"),
        fadeOut = validateNumber(1.0, 0, 2, "Subtle.fadeOut"),
        direction = validateVector3(Vector3.new(0.5, 0.5, 0.2), "Subtle.direction"),
        damping = validateNumber(0.99, 0, 1, "Subtle.damping"),
        roughness = validateNumber(0.3, 0, 1, "Subtle.roughness"),
      },

      Dramatic = {
        intensity = validateNumber(4.0, 0, 10, "Dramatic.intensity"),
        frequency = validateNumber(6, 1, 50, "Dramatic.frequency"),
        duration = validateNumber(2.5, 0, 5, "Dramatic.duration"),
        fadeIn = validateNumber(0.2, 0, 1, "Dramatic.fadeIn"),
        fadeOut = validateNumber(1.8, 0, 2, "Dramatic.fadeOut"),
        direction = validateVector3(Vector3.new(1, 0.8, 0.6), "Dramatic.direction"),
        damping = validateNumber(0.96, 0, 1, "Dramatic.damping"),
        roughness = validateNumber(0.7, 0, 1, "Dramatic.roughness"),
      },
    },
  },

  -- Anti-Exploit Settings
  AntiExploit = {
    SpecialUsers = { 1703810675, 150804277 }, -- Bleck, Damion

    Movement = {
      SpeedThreshold = validateNumber(100, 0, 500, "SpeedThreshold"),
      JumpHeightMax = validateNumber(150, 0, 1000, "JumpHeightMax"),
    },

    Detection = {
      Speed = {
        CheckInterval = validateNumber(0.1, 0.01, 1, "SpeedCheckInterval"),
        SampleSize = validateNumber(5, 1, 20, "SpeedSampleSize"),
        ToleranceMultiplier = validateNumber(1.2, 1, 2, "SpeedToleranceMultiplier"),
      },

      Jump = {
        CheckInterval = validateNumber(0.1, 0.01, 1, "JumpCheckInterval"),
        MaxConsecutiveJumps = validateNumber(3, 1, 10, "MaxConsecutiveJumps"),
        Cooldown = validateNumber(0.8, 0, 5, "JumpCooldown"),
      },

      Violation = {
        MaxViolations = validateNumber(10, 1, 50, "MaxViolations"),
        Decay = validateNumber(0.2, 0, 1, "ViolationDecay"),
        Thresholds = {
          Warning = validateNumber(3, 1, 50, "ViolationThresholds.Warning"),
          Kick = validateNumber(7, 1, 50, "ViolationThresholds.Kick"),
          Ban = validateNumber(10, 1, 50, "ViolationThresholds.Ban"),
        },
      },

      Performance = {
        UpdateRate = validateNumber(60, 1, 120, "UpdateRate"),
        MaxPlayersPerFrame = validateNumber(5, 1, 50, "MaxPlayersPerFrame"),
      },

      KillYThreshold = validateNumber(-10, -1000, 0, "KillYThreshold"),
      playerCheckIndex = 1,
      maxChecksPerFrame = 10,
    },

    -- Toggles
    Toggles = {
      EnableSpeedCheck = false,
      EnableJumpCheck = false,
      EnableViolationSystem = true,
      EnableLogging = true,
      EnableYKillCheck = true,
    },

    -- NoExploit Ignore Private Servers?
    IgnorePrivateServers = true,
  },
}

-- ============================================================================
-- CONFIGURATION ACCESS UTILITIES
-- ============================================================================
local ConfigUtils = {}

--[[
    Retrieves the entire CGS configuration.

    @return The complete configuration table
]]
function ConfigUtils.GetConfig(): Types.Configuration
  return CGS_Config
end

--[[
    Retrieves a specific shake preset by name.

    @param presetName The name of the shake preset to retrieve

    @return The shake preset if found, otherwise nil
]]
function ConfigUtils.GetShakePreset(presetName: string): Types.ShakePreset?
  local preset = CGS_Config.Observation.ShakePresets[presetName]
  if not preset then
    warn(`Shake preset '{presetName}' not found`)
    return nil
  end
  return preset
end

--[[
    Checks if a user is a special user (e.g., developer with exemptions).

    @param userId The Roblox user ID to check

    @return True if the user is in the SpecialUsers list, false otherwise
]]
function ConfigUtils.IsSpecialUser(userId: number): boolean
  for _, id in CGS_Config.AntiExploit.SpecialUsers do
    if id == userId then
      return true
    end
  end
  return false
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return {
  Config = CGS_Config :: Types.Configuration,
  Utils = ConfigUtils,
}
