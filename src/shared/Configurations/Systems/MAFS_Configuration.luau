--!strict

--[[
    - file: MAFS_CONFIGURATION.LUAU

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized configuration for the MAFS System.
      - Organized into modular sections for easier maintenance and use.
      - Includes runtime validation and utility functions for safe access.

    - dependencies:
      - MAFS_Types

    - notes:
      - Utility Functions must not be moved to MAFS_Utils and MAFS_Utils shall not be required because:
       - (Type Error: Cyclic module dependency) will appear in Script Analysis if you do so.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")
local Types = require(mainPath:WaitForChild("MAFS_Types"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local DEFAULT_SOUND_RADIUS = 50
local DEFAULT_FOOTPRINT_LIFETIME = 8
local DEFAULT_UPDATE_INTERVAL = 0.1
local DEFAULT_VELOCITY_THRESHOLD = 1
local DEFAULT_VOLUME = 0.5
local DEFAULT_MAX_CONCURRENT_SOUNDS = 10
local STEP_INTERVAL_BASE = 0.4
local FOOTPRINT_OFFSET_Y = -3
local MIN_VOLUME = 0.0
local MAX_VOLUME = 1.0
local MIN_RESOLUTION_TIME_WARNING = 5 -- milliseconds
local MAX_ERROR_RATE_WARNING = 0.05 -- 5%

-- ============================================================================
-- TYPES
-- ============================================================================
export type MAFSConfiguration = Types.MAFSConfiguration

-- ============================================================================
-- MATERIAL SOUND MAPPINGS
-- ============================================================================
local FootstepSoundMap: Types.SoundConfigMap = {
  [Enum.Material.Air] = "Air",
  [Enum.Material.Asphalt] = "Concrete",
  [Enum.Material.Basalt] = "Cobblestone",
  [Enum.Material.Brick] = "Brick",
  [Enum.Material.Carpet] = "Fabric",
  [Enum.Material.Cobblestone] = "Cobblestone",
  [Enum.Material.Concrete] = "Concrete",
  [Enum.Material.CorrodedMetal] = "Metal",
  [Enum.Material.CrackedLava] = "Cobblestone",
  [Enum.Material.DiamondPlate] = "DiamondPlate",
  [Enum.Material.Fabric] = "Fabric",
  [Enum.Material.Foil] = "Foil",
  [Enum.Material.ForceField] = "ForceField",
  [Enum.Material.Glass] = "Glass",
  [Enum.Material.Glacier] = "Ice",
  [Enum.Material.Granite] = "Granite",
  [Enum.Material.Grass] = "Grass",
  [Enum.Material.Ground] = "Grass",
  [Enum.Material.Ice] = "Ice",
  [Enum.Material.LeafyGrass] = "Grass",
  [Enum.Material.Leather] = "Fabric",
  [Enum.Material.Limestone] = "Slate",
  [Enum.Material.Marble] = "Marble",
  [Enum.Material.Metal] = "Metal",
  [Enum.Material.Mud] = "Grass",
  [Enum.Material.Neon] = "Neon",
  [Enum.Material.Pavement] = "Concrete",
  [Enum.Material.Pebble] = "Pebble",
  [Enum.Material.Plastic] = "Plastic",
  [Enum.Material.Rock] = "Cobblestone",
  [Enum.Material.Salt] = "Sand",
  [Enum.Material.Sand] = "Sand",
  [Enum.Material.Slate] = "Cobblestone",
  [Enum.Material.SmoothPlastic] = "SmoothPlastic",
  [Enum.Material.Snow] = "Snow",
  [Enum.Material.Water] = "",
  [Enum.Material.Wood] = "Wood",
  [Enum.Material.WoodPlanks] = "WoodPlanks",
}

-- ============================================================================
-- FOOTPRINT MATERIAL MAPPINGS
-- ============================================================================
local FootprintMaterials: Types.FootprintConfigMap = {
  [Enum.Material.Sand] = "SandFootprint",
  [Enum.Material.Mud] = "MudFootprint",
  [Enum.Material.Snow] = "SnowFootprint",
}

-- ============================================================================
-- CORE CONFIGURATION
-- ============================================================================
local MAFSConfig: MAFSConfiguration = {
  -- Core Settings
  performanceMonitor = false, -- Set to true to enable performance monitoring
  debugMode = false,
  enableFootsteps = true,
  enableFootprints = true,

  -- Audio Settings
  soundRadius = DEFAULT_SOUND_RADIUS,
  defaultVolume = DEFAULT_VOLUME,
  volumeRange = {
    min = MIN_VOLUME,
    max = MAX_VOLUME,
  },

  -- Footprint Settings
  footprintLifetime = DEFAULT_FOOTPRINT_LIFETIME,
  footprintOffset = Vector3.new(0, FOOTPRINT_OFFSET_Y, 0),

  -- Performance Settings
  updateInterval = DEFAULT_UPDATE_INTERVAL,
  velocityThreshold = DEFAULT_VELOCITY_THRESHOLD,
  maxConcurrentSounds = DEFAULT_MAX_CONCURRENT_SOUNDS,

  -- Material Mappings
  footstepSoundMap = FootstepSoundMap,
  footprintMaterials = FootprintMaterials,

  -- Default Values
  defaultMaterial = "Grass",
  fallbackSound = "Grass",
}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Get configuration value with validation
local function getConfigValue<T>(key: string, defaultValue: T): T
  local value = (MAFSConfig :: any)[key]
  if value ~= nil then
    return value
  end
  return defaultValue
end

-- Validate volume range
local function validateVolume(volume: number): number
  return math.clamp(volume, MAFSConfig.volumeRange.min, MAFSConfig.volumeRange.max)
end

-- Get sound name for material with fallback
local function getSoundForMaterial(material: Enum.Material): string
  return MAFSConfig.footstepSoundMap[material] or MAFSConfig.fallbackSound
end

-- Get footprint name for material
local function getFootprintForMaterial(material: Enum.Material): string?
  return MAFSConfig.footprintMaterials[material]
end

-- Check if performance monitoring is enabled
local function isPerformanceMonitorEnabled(): boolean
  return MAFSConfig.performanceMonitor
end

-- Check if debug mode is enabled
local function isDebugModeEnabled(): boolean
  return MAFSConfig.debugMode
end

-- Validate configuration on load
local function validateConfiguration(): boolean
  local isValid = true

  -- Check required values
  if MAFSConfig.soundRadius <= 0 then
    warn("[MAFS_Config] Invalid soundRadius: must be greater than 0")
    isValid = false
  end

  if MAFSConfig.footprintLifetime <= 0 then
    warn("[MAFS_Config] Invalid footprintLifetime: must be greater than 0")
    isValid = false
  end

  if MAFSConfig.updateInterval <= 0 then
    warn("[MAFS_Config] Invalid updateInterval: must be greater than 0")
    isValid = false
  end

  if MAFSConfig.velocityThreshold < 0 then
    warn("[MAFS_Config] Invalid velocityThreshold: must be non-negative")
    isValid = false
  end

  return isValid
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Validate configuration on load
if not validateConfiguration() then
  error("[MAFS_Config] Configuration validation failed")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================

return {
  -- Core configuration
  Config = MAFSConfig,

  -- Utility functions
  getConfigValue = getConfigValue,
  validateVolume = validateVolume,
  getSoundForMaterial = getSoundForMaterial,
  getFootprintForMaterial = getFootprintForMaterial,
  isPerformanceMonitorEnabled = isPerformanceMonitorEnabled,
  isDebugModeEnabled = isDebugModeEnabled,

  -- Legacy compatibility (for existing code)
  FootstepSoundMap = FootstepSoundMap,
  FootprintMaterials = FootprintMaterials,
  SoundRadius = MAFSConfig.soundRadius,
  FootprintLifetime = MAFSConfig.footprintLifetime,
}
