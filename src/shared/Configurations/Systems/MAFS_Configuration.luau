--!strict
local Config = {}

local EnumMaterial = Enum.Material

-- inside MAFS_Configuration
print("[MAFS_Config] Loading")

Config.FootprintMaterials = {
  [EnumMaterial.Sand] = "SandFootprint",
  [EnumMaterial.Mud] = "MudFootprint",
  [EnumMaterial.Snow] = "SnowFootprint",
}

Config.FootstepSoundMap = {
  [Enum.Material.Air] = "Air",
  [Enum.Material.Asphalt] = "Concrete",
  [Enum.Material.Basalt] = "Cobblestone",
  [Enum.Material.Brick] = "Brick",
  [Enum.Material.Carpet] = "Fabric",
  [Enum.Material.Cobblestone] = "Cobblestone",
  [Enum.Material.Concrete] = "Concrete",
  [Enum.Material.CorrodedMetal] = "Metal",
  [Enum.Material.CrackedLava] = "Cobblestone",
  [Enum.Material.DiamondPlate] = "DiamondPlate",
  [Enum.Material.Fabric] = "Fabric",
  [Enum.Material.Foil] = "Foil",
  [Enum.Material.ForceField] = "ForceField",
  [Enum.Material.Glass] = "Glass",
  [Enum.Material.Glacier] = "Ice",
  [Enum.Material.Granite] = "Granite",
  [Enum.Material.Grass] = "Grass",
  [Enum.Material.Ground] = "Grass",
  [Enum.Material.Ice] = "Ice",
  [Enum.Material.LeafyGrass] = "Grass",
  [Enum.Material.Leather] = "Fabric",
  [Enum.Material.Limestone] = "Slate",
  [Enum.Material.Marble] = "Marble",
  [Enum.Material.Metal] = "Metal",
  [Enum.Material.Mud] = "Grass",
  [Enum.Material.Neon] = "Neon",
  [Enum.Material.Pavement] = "Concrete",
  [Enum.Material.Pebble] = "Pebble",
  [Enum.Material.Plastic] = "Plastic",
  [Enum.Material.Rock] = "Cobblestone",
  [Enum.Material.Salt] = "Sand",
  [Enum.Material.Sand] = "Sand",
  [Enum.Material.Slate] = "Cobblestone",
  [Enum.Material.SmoothPlastic] = "SmoothPlastic",
  [Enum.Material.Snow] = "Snow",
  [Enum.Material.Water] = "",
  [Enum.Material.Wood] = "Wood",
  [Enum.Material.WoodPlanks] = "WoodPlanks",
}

Config.SoundRadius = 50
Config.FootprintLifetime = 8

print("[MAFS_Config] Loaded")

return Config
