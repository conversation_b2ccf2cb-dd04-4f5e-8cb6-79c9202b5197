--!strict

--[[
    - file: MCS_Test_Validation.luau
    
    - version: 1.0.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Test validation script for MCS fixes
      - Validates UI feedback, player suggestions, and system consistency
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- TEST VALIDATION
-- ============================================================================
local TestValidation = {}

-- Test 1: Validate centralized player command configuration
function TestValidation.testPlayerCommandConfiguration(): boolean
  print("Testing player command configuration...")

  -- Check if PLAYER_ARGUMENT_COMMANDS exists and is not empty
  local playerCommands = Configuration.Commands.PLAYER_ARGUMENT_COMMANDS
  if not playerCommands or type(playerCommands) ~= "table" or #playerCommands == 0 then
    warn("FAIL: PLAYER_ARGUMENT_COMMANDS not properly configured")
    return false
  end

  -- Check if expected commands are present
  local expectedCommands = { "ban", "kick", "grantperm", "revokeperm" }
  for _, expectedCmd in ipairs(expectedCommands) do
    local found = false
    for _, configCmd in ipairs(playerCommands) do
      if configCmd == expectedCmd then
        found = true
        break
      end
    end
    if not found then
      warn("FAIL: Expected command '" .. expectedCmd .. "' not found in PLAYER_ARGUMENT_COMMANDS")
      return false
    end
  end

  print("PASS: Player command configuration is valid")
  return true
end

-- Test 2: Validate player name matching utility
function TestValidation.testPlayerNameMatching(): boolean
  print("Testing player name matching utility...")

  -- Create a mock player for testing
  local mockPlayer = {
    Name = "TestPlayer123",
    DisplayName = "TestDisplay",
  }

  -- Test exact name match
  if not Utils.playerNameMatches(mockPlayer, "TestPlayer123") then
    warn("FAIL: Exact name match failed")
    return false
  end

  -- Test partial name match
  if not Utils.playerNameMatches(mockPlayer, "Test") then
    warn("FAIL: Partial name match failed")
    return false
  end

  -- Test display name match
  if not Utils.playerNameMatches(mockPlayer, "TestDisplay") then
    warn("FAIL: Display name match failed")
    return false
  end

  -- Test partial display name match
  if not Utils.playerNameMatches(mockPlayer, "TestDis") then
    warn("FAIL: Partial display name match failed")
    return false
  end

  -- Test case insensitive match
  if not Utils.playerNameMatches(mockPlayer, "testplayer") then
    warn("FAIL: Case insensitive match failed")
    return false
  end

  -- Test non-match
  if Utils.playerNameMatches(mockPlayer, "NonExistent") then
    warn("FAIL: Non-match incorrectly returned true")
    return false
  end

  print("PASS: Player name matching utility works correctly")
  return true
end

-- Test 3: Validate configuration consistency
function TestValidation.testConfigurationConsistency(): boolean
  print("Testing configuration consistency...")

  -- Check if all required configuration fields exist
  local requiredFields = {
    "PREFIX",
    "MAX_COMMAND_LENGTH",
    "MAX_ARGS",
    "MAX_SUGGESTIONS",
    "PLAYER_ARGUMENT_COMMANDS",
  }

  for _, field in ipairs(requiredFields) do
    if Configuration.Commands[field] == nil then
      warn("FAIL: Required configuration field '" .. field .. "' is missing")
      return false
    end
  end

  -- Validate field types
  if type(Configuration.Commands.PREFIX) ~= "string" then
    warn("FAIL: PREFIX should be a string")
    return false
  end

  if type(Configuration.Commands.MAX_COMMAND_LENGTH) ~= "number" then
    warn("FAIL: MAX_COMMAND_LENGTH should be a number")
    return false
  end

  if type(Configuration.Commands.PLAYER_ARGUMENT_COMMANDS) ~= "table" then
    warn("FAIL: PLAYER_ARGUMENT_COMMANDS should be a table")
    return false
  end

  print("PASS: Configuration consistency is valid")
  return true
end

-- Test 4: Validate utility functions
function TestValidation.testUtilityFunctions(): boolean
  print("Testing utility functions...")

  -- Test hasPrefix function
  local testText = "!ban player123"
  if not Utils.hasPrefix(testText, "!") then
    warn("FAIL: hasPrefix function failed")
    return false
  end

  -- Test stripPrefix function
  local stripped = Utils.stripPrefix(testText, "!")
  if stripped ~= "ban player123" then
    warn("FAIL: stripPrefix function failed, got: " .. tostring(stripped))
    return false
  end

  -- Test splitCommandText function
  local tokens = Utils.splitCommandText("ban player123 reason")
  if #tokens ~= 3 or tokens[1] ~= "ban" or tokens[2] ~= "player123" or tokens[3] ~= "reason" then
    warn("FAIL: splitCommandText function failed")
    return false
  end

  print("PASS: Utility functions work correctly")
  return true
end

-- Run all tests
function TestValidation.runAllTests(): boolean
  print("=== MCS Test Validation Started ===")

  local tests = {
    TestValidation.testPlayerCommandConfiguration,
    TestValidation.testPlayerNameMatching,
    TestValidation.testConfigurationConsistency,
    TestValidation.testUtilityFunctions,
  }

  local passed = 0
  local total = #tests

  for i, test in ipairs(tests) do
    local success = pcall(test)
    if success then
      passed = passed + 1
    else
      warn("Test " .. i .. " failed with error")
    end
  end

  print("=== MCS Test Validation Complete ===")
  print("Passed: " .. passed .. "/" .. total .. " tests")

  if passed == total then
    print("✅ All tests passed! MCS fixes are working correctly.")
    return true
  else
    warn("❌ Some tests failed. Please review the issues above.")
    return false
  end
end

return TestValidation
