--!strict

--[[
	- file: CGS_Remotes.luau

	- version: 1.0.0
	- author: BleckWolf25
	- contributors:

	- copyright: Dynamic Innovative Studio

	- description:
		- Initializes all necessary RemoteEvents and RemoteFunctions for the system.
		- Automatically creates a Remotes folder if it doesn’t exist.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage:WaitForChild("CGS"):WaitForChild("CGS_Types"))
local Utils =
  require(ReplicatedStorage:WaitForChild("CGS"):WaitForChild("Shared"):WaitForChild("CGS_Utils"))

-- ============================================================================
-- VARIABLES
-- ============================================================================
local REMOTE_EVENT = "RemoteEvent" :: "RemoteEvent"
local REMOTE_FUNCTION = "RemoteFunction" :: "RemoteFunction"
local TAG = "Remotes" :: string

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

-- Get the Folder
local function getRemotesFolder(): Folder
  Utils.log(TAG, "Getting remotes folder")
  local cgsFolder = ReplicatedStorage:WaitForChild("CGS")
  local folder = cgsFolder:FindFirstChild("Remotes") :: Folder?
  if folder then
    Utils.log(TAG, "Remotes folder found")
    return folder
  else
    Utils.log(TAG, "Remotes folder not found, creating")
    local newFolder = Instance.new("Folder")
    newFolder.Name = "Remotes"
    newFolder.Parent = cgsFolder
    Utils.log(TAG, "Remotes folder created")
    return newFolder
  end
end

-- Initialization
function init(): ()
  Utils.log(TAG, "Starting remotes initialization")
  local remotesFolder = getRemotesFolder()
  Utils.log(TAG, "Remotes folder obtained")

  local validRemoteTypes = {
    RemoteEvent = true,
    RemoteFunction = true,
  }

  local function createRemote(
    folder: Folder,
    name: string,
    className: "RemoteEvent" | "RemoteFunction"
  )
    if not validRemoteTypes[className] then
      Utils.warn(TAG, `Invalid remote type: {className}`)
      return
    end

    local remote = folder:FindFirstChild(name)
    if remote then
      Utils.log(TAG, `Remote {name} already exists`)
    else
      local newRemote
      if className == REMOTE_EVENT then
        newRemote = Instance.new("RemoteEvent")
      else
        newRemote = Instance.new("RemoteFunction")
      end

      newRemote.Name = name
      newRemote.Parent = folder
      Utils.log(TAG, `Created remote: {name} ({className})`)
    end
  end

  local RemoteDefinitions = {
    { name = "DirectionalHeadLook", className = REMOTE_EVENT },
    { name = "GetAnimation", className = REMOTE_FUNCTION },
    { name = "staminaUpdate", className = REMOTE_EVENT },
    { name = "jumpRequest", className = REMOTE_EVENT },
    { name = "exhaustEvent", className = REMOTE_EVENT },
    { name = "ToggleCameraEffects", className = REMOTE_EVENT },
    { name = "TriggerCinematicEffect", className = REMOTE_EVENT },
  }

  for _, def in ipairs(RemoteDefinitions) do
    createRemote(remotesFolder, def.name, def.className)
  end
  Utils.log(TAG, "Finished remotes initialization")
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CGS_Remotes: Types.CGS_Remotes = {
  init = init,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CGS_Remotes
