--!strict

--[[
    - file: CGS_DirectionalMovement.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Directional Movement for CGS -> Movement Sub-system
      - Adds a realistic walking effect that makes characters move like real life.

    - dependencies:
      - CGS_Types
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.CGS.CGS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local BodyAnimation = {}

-- ============================================================================
-- ANGLE CONFIGURATIONS
-- ============================================================================
local ANGLE_CONFIGS: {
  [string]: { [Types.JointName]: { number } },
  StateMultipliers: { [Types.MovementState]: number },
} =
  {
    Forward = {
      RootJoint = { -0.09, 0, 0 },
      Neck = { 0.05, 0, 0 },
      RightShoulder = { 0, 0, 0 },
      LeftShoulder = { 0, 0, 0 },
      RightHip = { 0, 0, 0 },
      LeftHip = { 0, 0, 0 },
    },

    -- Backward movement
    Backward = {
      RootJoint = { 0.12, 0, 0 }, -- Backward lean
      Neck = { -0.08, 0, 0 },
      RightShoulder = { 0, 0, 0 },
      LeftShoulder = { 0, 0, 0 },
      RightHip = { 0, 0, 0 },
      LeftHip = { 0, 0, 0 },
    },

    -- Right strafe
    StrafeRight = {
      RootJoint = { 0, 0, -0.15 }, -- Lean into turn
      Neck = { 0, 0, 0.12 },
      RightShoulder = { 0, -0.08, 0 },
      LeftShoulder = { 0, -0.08, 0 },
      RightHip = { 0, -0.1, 0 },
      LeftHip = { 0, -0.1, 0 },
    },

    -- Left strafe
    StrafeLeft = {
      RootJoint = { 0, 0, 0.15 },
      Neck = { 0, 0, -0.12 },
      RightShoulder = { 0, 0.08, 0 },
      LeftShoulder = { 0, 0.08, 0 },
      RightHip = { 0, 0.1, 0 },
      LeftHip = { 0, 0.1, 0 },
    },

    -- Diagonal movements (reduced intensity)
    ForwardRight = {
      RootJoint = { -0.06, 0, -0.08 },
      Neck = { 0.03, 0, 0.06 },
      RightShoulder = { 0, -0.05, 0 },
      LeftShoulder = { 0, -0.05, 0 },
      RightHip = { 0, -0.06, 0 },
      LeftHip = { 0, -0.06, 0 },
    },

    ForwardLeft = {
      RootJoint = { -0.06, 0, 0.08 },
      Neck = { 0.03, 0, -0.06 },
      RightShoulder = { 0, 0.05, 0 },
      LeftShoulder = { 0, 0.05, 0 },
      RightHip = { 0, 0.06, 0 },
      LeftHip = { 0, 0.06, 0 },
    },

    BackwardRight = {
      RootJoint = { 0.08, 0, -0.06 },
      Neck = { -0.05, 0, 0.04 },
      RightShoulder = { 0, -0.04, 0 },
      LeftShoulder = { 0, -0.04, 0 },
      RightHip = { 0, -0.05, 0 },
      LeftHip = { 0, -0.05, 0 },
    },

    BackwardLeft = {
      RootJoint = { 0.08, 0, 0.06 },
      Neck = { -0.05, 0, -0.04 },
      RightShoulder = { 0, 0.04, 0 },
      LeftShoulder = { 0, 0.04, 0 },
      RightHip = { 0, 0.05, 0 },
      LeftHip = { 0, 0.05, 0 },
    },

    -- State-specific modifications
    StateMultipliers = {
      Idle = 0,
      Walk = 1,
      Sprint = 1.3,
      Crouch = 0.6,
      Prone = 0.2,
      Jump = 0.8,
    },
  }

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

--[[
    Creates a BodyAnimator object for a given character.

    @param character Model -- The character model to create the animator for.

    @return Types.BodyAnimator? -- Returns a BodyAnimator table if successful, nil otherwise.
]]
function BodyAnimation.CreateAnimator(character: Model): Types.BodyAnimator?
  local torso = character:FindFirstChild("Torso") :: Part?
  local rootPart = character:FindFirstChild("HumanoidRootPart") :: Part?
  if not torso or not rootPart then
    return nil
  end

  local joints: { [Types.JointName]: Motor6D? } = {
    RootJoint = rootPart:FindFirstChild("RootJoint") :: Motor6D?,
    Neck = torso:FindFirstChild("Neck") :: Motor6D?,
    RightShoulder = torso:FindFirstChild("Right Shoulder") :: Motor6D?,
    LeftShoulder = torso:FindFirstChild("Left Shoulder") :: Motor6D?,
    RightHip = torso:FindFirstChild("Right Hip") :: Motor6D?,
    LeftHip = torso:FindFirstChild("Left Hip") :: Motor6D?,
  }

  local jointData: { [Types.JointName]: Types.JointData } = {}
  for name, joint in pairs(joints) do
    if joint then
      jointData[name] = {
        Joint = joint,
        OriginalC0 = joint.C0,
        CurrentTilt = CFrame.new(),
      }
    end
  end

  return {
    Joints = jointData,
    TweenInfo = TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
    ActiveTweens = {},
  }
end

--[[
    Determines the movement direction key based on input vector.

    @param inputDirection Vector3 -- The input direction vector.

    @return string -- Returns a direction key such as "Forward", "StrafeLeft", "Idle", etc.
]]
function BodyAnimation.GetDirectionKey(inputDirection: Vector3): string
  local threshold = 0.3
  local x, z = inputDirection.X, inputDirection.Z

  if math.abs(x) < threshold and z < -threshold then
    return "Forward"
  end
  if math.abs(x) < threshold and z > threshold then
    return "Backward"
  end
  if x > threshold and math.abs(z) < threshold then
    return "StrafeRight"
  end
  if x < -threshold and math.abs(z) < threshold then
    return "StrafeLeft"
  end

  if x > threshold and z < -threshold then
    return "ForwardRight"
  end
  if x < -threshold and z < -threshold then
    return "ForwardLeft"
  end
  if x > threshold and z > threshold then
    return "BackwardRight"
  end
  if x < -threshold and z > threshold then
    return "BackwardLeft"
  end

  return "Idle"
end

--[[
    Updates the animation of the body joints based on input direction and movement state.

    @param animator Types.BodyAnimator -- The animator to update.
    @param inputDirection Vector3 -- The direction vector from input.
    @param movementState Types.MovementState -- The current movement state (e.g., Walk, Sprint).
]]
function BodyAnimation.UpdateAnimation(
  animator: Types.BodyAnimator,
  inputDirection: Vector3,
  movementState: Types.MovementState
)
  local directionKey = BodyAnimation.GetDirectionKey(inputDirection)
  local rawAngles = ANGLE_CONFIGS[directionKey]

  local angles: { [Types.JointName]: { number } }
  if rawAngles ~= nil then
    angles = rawAngles :: { [Types.JointName]: { number } }
  else
    angles = {
      RootJoint = { 0, 0, 0 },
      Neck = { 0, 0, 0 },
      RightShoulder = { 0, 0, 0 },
      LeftShoulder = { 0, 0, 0 },
      RightHip = { 0, 0, 0 },
      LeftHip = { 0, 0, 0 },
    }
  end

  local multiplier = ANGLE_CONFIGS.StateMultipliers[movementState] or 1

  for jointName, jointData in pairs(animator.Joints) do
    local rawTargetAngles = angles[jointName]
    if rawTargetAngles == nil then
      continue
    end

    local targetAngles = rawTargetAngles :: { number }

    local targetCFrame = CFrame.Angles(
      targetAngles[1] * multiplier,
      targetAngles[2] * multiplier,
      targetAngles[3] * multiplier
    )

    local startTime = tick()
    local duration = animator.TweenInfo.Time
    local startTilt = jointData.CurrentTilt

    local activeTween = animator.ActiveTweens[jointName]
    if activeTween then
      activeTween:Disconnect()
      animator.ActiveTweens[jointName] = nil
    end

    local connection: RBXScriptConnection
    connection = RunService.RenderStepped:Connect(function()
      local elapsed = tick() - startTime
      local alpha = math.clamp(elapsed / duration, 0, 1)
      local interpolated = startTilt:Lerp(targetCFrame, alpha)

      jointData.CurrentTilt = interpolated
      jointData.Joint.C0 = jointData.OriginalC0 * interpolated

      if alpha >= 1 then
        connection:Disconnect()
        animator.ActiveTweens[jointName] = nil
      end
    end)

    animator.ActiveTweens[jointName] = connection
  end
end

--[[
    Applies current tilt transforms to all joints to update their C0 property.

    @param animator Types.BodyAnimator -- The animator whose joints will be updated.
]]
function BodyAnimation.UpdateJoints(animator: Types.BodyAnimator)
  for _, jointData in pairs(animator.Joints) do
    jointData.Joint.C0 = jointData.OriginalC0 * jointData.CurrentTilt
  end
end

--[[
    Resets the animation by tweening all joints back to their original orientation.

    @param animator Types.BodyAnimator -- The animator to reset.
]]
function BodyAnimation.ResetAnimation(animator: Types.BodyAnimator)
  for jointName, jointData in pairs(animator.Joints) do
    local activeTween = animator.ActiveTweens[jointName]
    if activeTween then
      activeTween:Disconnect()
      animator.ActiveTweens[jointName] = nil
    end

    local startTime = tick()
    local duration = animator.TweenInfo.Time
    local startTilt = jointData.CurrentTilt

    local connection: RBXScriptConnection
    connection = RunService.RenderStepped:Connect(function()
      local elapsed = tick() - startTime
      local alpha = math.clamp(elapsed / duration, 0, 1)
      local interpolated = startTilt:Lerp(CFrame.new(), alpha)

      jointData.CurrentTilt = interpolated
      jointData.Joint.C0 = jointData.OriginalC0 * interpolated

      if alpha >= 1 then
        connection:Disconnect()
        animator.ActiveTweens[jointName] = nil
      end
    end)

    animator.ActiveTweens[jointName] = connection
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return BodyAnimation
