--!strict

--[[
    - file: MAFS_REMOTES.LUAU

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized remote event management for the MAFS System.
      - Provides type-safe remote event creation and access.
      - Handles automatic folder creation and remote event initialization.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils

    - notes:
      - Creates remote events on-demand with proper error handling.
      - Provides centralized access to all MAFS remote events.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local REMOTES_FOLDER_NAME = "Remotes"
local REMOTE_CREATION_TIMEOUT = 10

-- ============================================================================
-- TYPES
-- ============================================================================
export type FootstepRemotes = Types.FootstepRemotes

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Get or create MAFS folder
local function getMAFSFolder(): Folder
  local mafsFolder = ReplicatedStorage:FindFirstChild("MAFS")
  if not mafsFolder then
    if RunService:IsServer() then
      mafsFolder = Instance.new("Folder")
      mafsFolder.Name = "MAFS"
      mafsFolder.Parent = ReplicatedStorage
      Utils.log("Created MAFS folder", "INFO")
    else
      mafsFolder = ReplicatedStorage:WaitForChild("MAFS", REMOTE_CREATION_TIMEOUT)
      if not mafsFolder then
        Utils.error("Failed to find MAFS folder after timeout")
      end
    end
  end
  return mafsFolder :: Folder
end

-- Get or create remotes folder
local function getRemotesFolder(): Folder
  local mafsFolder = getMAFSFolder()
  local remotesFolder = mafsFolder:FindFirstChild(REMOTES_FOLDER_NAME)

  if not remotesFolder then
    if RunService:IsServer() then
      remotesFolder = Instance.new("Folder")
      remotesFolder.Name = REMOTES_FOLDER_NAME
      remotesFolder.Parent = mafsFolder
      Utils.log("Created remotes folder", "INFO")
    else
      remotesFolder = mafsFolder:WaitForChild(REMOTES_FOLDER_NAME, REMOTE_CREATION_TIMEOUT)
      if not remotesFolder then
        Utils.error("Failed to find remotes folder after timeout")
      end
    end
  end

  return remotesFolder :: Folder
end

-- Create or get remote event with validation
local function getOrCreateRemoteEvent(name: string): RemoteEvent
  local remotesFolder = getRemotesFolder()
  local existingRemote = remotesFolder:FindFirstChild(name)

  if existingRemote then
    if existingRemote:IsA("RemoteEvent") then
      return existingRemote :: RemoteEvent
    else
      Utils.warn(`Found non-RemoteEvent instance named '{name}', removing it`)
      existingRemote:Destroy()
    end
  end

  -- Create new remote event
  if RunService:IsServer() then
    local newRemote = Instance.new("RemoteEvent")
    newRemote.Name = name
    newRemote.Parent = remotesFolder
    Utils.log(`Created remote event: {name}`, "DEBUG")
    return newRemote
  else
    -- Client waits for server to create it
    local remote = remotesFolder:WaitForChild(name, REMOTE_CREATION_TIMEOUT)
    if not remote or not remote:IsA("RemoteEvent") then
      Utils.error(`Failed to find or invalid remote event: {name}`)
    end
    return remote :: RemoteEvent
  end
end

-- ============================================================================
-- REMOTE EVENT DEFINITIONS
-- ============================================================================

-- Define all remote events used by MAFS
local RemoteEventNames = {
  PlayFootstep = "PlayFootstep",
  StartFootstepSound = "StartFootstepSound",
  StopFootstepSound = "StopFootstepSound",
  UpdateFootstepSound = "UpdateFootstepSound",
  ReplicateFootprint = "ReplicateFootprint",
  StartReplicatedFootstepSound = "StartReplicatedFootstepSound",
  StopReplicatedFootstepSound = "StopReplicatedFootstepSound",
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

-- Create remote events table
local MAFSRemotes: FootstepRemotes = {
  PlayFootstep = getOrCreateRemoteEvent(RemoteEventNames.PlayFootstep),
  StartFootstepSound = getOrCreateRemoteEvent(RemoteEventNames.StartFootstepSound),
  StopFootstepSound = getOrCreateRemoteEvent(RemoteEventNames.StopFootstepSound),
  UpdateFootstepSound = getOrCreateRemoteEvent(RemoteEventNames.UpdateFootstepSound),
  ReplicateFootprint = getOrCreateRemoteEvent(RemoteEventNames.ReplicateFootprint),
  StartReplicatedFootstepSound = getOrCreateRemoteEvent(
    RemoteEventNames.StartReplicatedFootstepSound
  ),
  StopReplicatedFootstepSound = getOrCreateRemoteEvent(
    RemoteEventNames.StopReplicatedFootstepSound
  ),
}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Get all remote events (for debugging/monitoring)
local function getAllRemotes(): { [string]: RemoteEvent }
  local remotes = {}
  for name, remote in pairs(MAFSRemotes) do
    remotes[name] = remote
  end
  return remotes
end

-- Check if all remotes are available
local function areAllRemotesReady(): boolean
  for name, remote in pairs(MAFSRemotes) do
    if not Utils.isValidInstance(remote) then
      Utils.warn(`Remote event '{name}' is not ready`)
      return false
    end
  end
  return true
end

-- Wait for all remotes to be ready (client-side)
local function waitForAllRemotes(timeout: number?): boolean
  timeout = timeout or REMOTE_CREATION_TIMEOUT
  local startTime = os.clock()

  while not areAllRemotesReady() do
    if os.clock() - startTime > timeout then
      Utils.error("Timeout waiting for remote events to be ready")
      return false
    end
    task.wait(0.1)
  end

  Utils.log("All remote events are ready", "INFO")
  return true
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize remotes on client
if RunService:IsClient() then
  task.spawn(function()
    if waitForAllRemotes() then
      Utils.log("MAFS remotes initialized successfully", "INFO")
    end
  end)
else
  Utils.log("MAFS remotes created on server", "INFO")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================

-- Export the remote events table with utility functions
return {
  -- Remote events
  PlayFootstep = MAFSRemotes.PlayFootstep,
  StartFootstepSound = MAFSRemotes.StartFootstepSound,
  StopFootstepSound = MAFSRemotes.StopFootstepSound,
  UpdateFootstepSound = MAFSRemotes.UpdateFootstepSound,
  ReplicateFootprint = MAFSRemotes.ReplicateFootprint,
  StartReplicatedFootstepSound = MAFSRemotes.StartReplicatedFootstepSound,
  StopReplicatedFootstepSound = MAFSRemotes.StopReplicatedFootstepSound,

  -- Utility functions
  getAllRemotes = getAllRemotes,
  areAllRemotesReady = areAllRemotesReady,
  waitForAllRemotes = waitForAllRemotes,
}
