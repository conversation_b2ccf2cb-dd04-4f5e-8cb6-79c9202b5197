--!strict
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MAFSFolder = ReplicatedStorage:WaitForChild("MAFS")

local RemotesFolder = MAFSFolder:FindFirstChild("Remotes") or Instance.new("Folder")
RemotesFolder.Name = "Remotes"
RemotesFolder.Parent = MAFSFolder

local function getOrCreate(name: string): RemoteEvent
	local r = RemotesFolder:FindFirstChild(name)
	if r and r:IsA("RemoteEvent") then return r end

	local newR = Instance.new("RemoteEvent")
	newR.Name = name
	newR.Parent = RemotesFolder
	return newR
end

return {
	PlayFootstep = getOrCreate("PlayFootstep"), -- Server → others
	StartFootstepSound = getOrCreate("StartFootstepSound"), -- Server → Local
	StopFootstepSound = getOrCreate("StopFootstepSound"), -- Server → Local
	ReplicateFootprint = getOrCreate("ReplicateFootprint"), -- Server → All
	StartReplicatedFootstepSound = getOrCreate("StartReplicatedFootstepSound"),
	StopReplicatedFootstepSound = getOrCreate("StopReplicatedFootstepSound"),
	UpdateFootstepSound =getOrCreate("UpdateFootstepSound")
}
