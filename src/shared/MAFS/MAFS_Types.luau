--!strict

-- All type definitions used in MAFS system

export type FootstepRemotes = {
	StartFootstepSound: RemoteEvent, -- <PERSON><PERSON> receives from Server
	UpdateFootstepSound: RemoteEvent,
	StopFootstepSound: RemoteEvent,

	StartReplicatedSound: RemoteEvent,
	StopReplicatedSound: RemoteEvent,

	ReplicateFootprint: RemoteEvent,
}

export type FootprintConfigMap = {
	[Enum.Material]: string,
}

export type SoundConfigMap = {
	[Enum.Material]: string,
}