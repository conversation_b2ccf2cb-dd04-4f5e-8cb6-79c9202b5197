--!strict

--[[
    - file: MAFS_PERFORMANCEMONITOR.LUAU

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Performance monitoring system for the MAFS (Material Audio Footstep System).
      - Tracks sound usage, efficiency metrics, and system performance.
      - Provides conditional activation based on configuration settings.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils

    - notes:
      - Only activates when performanceMonitor is enabled in configuration.
      - Provides comprehensive metrics collection and reporting capabilities.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MAX_RECENT_TIMES = 100
local METRICS_RESET_INTERVAL = 300 -- 5 minutes
local WARNING_COOLDOWN = 30 -- seconds
local REPORT_GENERATION_INTERVAL = 60 -- seconds
local MIN_RESOLUTION_TIME_WARNING = 5 -- milliseconds
local MAX_ERROR_RATE_WARNING = 0.05 -- 5%

-- ============================================================================
-- TYPES
-- ============================================================================
export type PerformanceMonitor = Types.PerformanceMonitor
export type PerformanceMetrics = Types.PerformanceMetrics
export type MAFSConfiguration = Types.MAFSConfiguration

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Calculate average from array of numbers
local function calculateAverage(numbers: { number }): number
  if #numbers == 0 then
    return 0
  end

  local sum = 0
  for _, num in numbers do
    sum += num
  end

  return sum / #numbers
end

-- Calculate efficiency percentage
local function calculateEfficiency(created: number, destroyed: number): number
  if created == 0 then
    return 100
  end
  return math.min(100, (destroyed / created) * 100)
end

-- Format number with appropriate units
local function formatNumber(num: number): string
  if num >= 1000000 then
    return string.format("%.1fM", num / 1000000)
  elseif num >= 1000 then
    return string.format("%.1fK", num / 1000)
  else
    return tostring(math.floor(num))
  end
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PerformanceMonitor = {}
PerformanceMonitor.__index = PerformanceMonitor

function PerformanceMonitor.new(config: MAFSConfiguration): PerformanceMonitor
  local self = setmetatable({}, PerformanceMonitor)

  -- Configuration
  self._config = config
  self._enabled = config.performanceMonitor
  self._startTime = os.clock()

  -- Metrics storage
  self._metrics = {
    basic = {
      SoundsCreated = 0,
      SoundsDestroyed = 0,
      FootprintsCreated = 0,
      MaterialResolutions = 0,
      NetworkRequests = 0,
      ErrorCount = 0,
      LastReset = os.clock(),
    },
    soundPoolEfficiency = 0,
    averageResolutionTime = 0,
    errorRate = 0,
    uptime = 0,
    materialUsageByType = {},
    errorsByType = {},
    recentResolutionTimes = {},
  }

  -- Warning tracking
  self._lastWarningTime = {}

  -- Connections
  self._connections = {}

  if self._enabled then
    self:_initializeMonitoring()
    Utils.log("Performance Monitor initialized", "INFO")
  else
    Utils.log("Performance Monitor disabled by configuration", "DEBUG")
  end

  return self
end

-- Check if monitoring is enabled
function PerformanceMonitor:isEnabled(): boolean
  return self._enabled
end

-- Record sound creation
function PerformanceMonitor:recordSoundCreation(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.SoundsCreated += 1
  self:_updateEfficiency()
end

-- Record sound destruction
function PerformanceMonitor:recordSoundDestruction(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.SoundsDestroyed += 1
  self:_updateEfficiency()
end

-- Record footprint creation
function PerformanceMonitor:recordFootprintCreation(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.FootprintsCreated += 1
end

-- Record material resolution with timing
function PerformanceMonitor:recordMaterialResolution(resolutionTime: number): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.MaterialResolutions += 1

  -- Add to recent times (keep only last MAX_RECENT_TIMES)
  table.insert(self._metrics.recentResolutionTimes, resolutionTime)
  if #self._metrics.recentResolutionTimes > MAX_RECENT_TIMES then
    table.remove(self._metrics.recentResolutionTimes, 1)
  end

  -- Update average
  self._metrics.averageResolutionTime = calculateAverage(self._metrics.recentResolutionTimes)

  -- Check for performance warnings
  if resolutionTime > MIN_RESOLUTION_TIME_WARNING then
    self:_checkWarning("resolution_time", "Material resolution took " .. resolutionTime .. "ms")
  end
end

-- Record network request
function PerformanceMonitor:recordNetworkRequest(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.NetworkRequests += 1
end

-- Record error with type
function PerformanceMonitor:recordError(errorType: string): ()
  if not self._enabled then
    return
  end

  self._metrics.basic.ErrorCount += 1

  -- Track error by type
  if not self._metrics.errorsByType[errorType] then
    self._metrics.errorsByType[errorType] = 0
  end
  self._metrics.errorsByType[errorType] += 1

  -- Update error rate
  self:_updateErrorRate()

  -- Check for error rate warnings
  if self._metrics.errorRate > MAX_ERROR_RATE_WARNING then
    self:_checkWarning(
      "error_rate",
      "High error rate: " .. string.format("%.2f%%", self._metrics.errorRate * 100)
    )
  end
end

-- Get current metrics
function PerformanceMonitor:getMetrics(): PerformanceMetrics
  if not self._enabled then
    return {
      basic = {
        SoundsCreated = 0,
        SoundsDestroyed = 0,
        FootprintsCreated = 0,
        MaterialResolutions = 0,
        NetworkRequests = 0,
        ErrorCount = 0,
        LastReset = 0,
      },
      soundPoolEfficiency = 0,
      averageResolutionTime = 0,
      errorRate = 0,
      uptime = 0,
      materialUsageByType = {},
      errorsByType = {},
      recentResolutionTimes = {},
    }
  end

  -- Update uptime
  self._metrics.uptime = os.clock() - self._startTime

  return self._metrics
end

-- Generate performance report
function PerformanceMonitor:generateReport(): string
  if not self._enabled then
    return "Performance monitoring is disabled"
  end

  local metrics = self:getMetrics()
  local report = {}

  table.insert(report, "=== MAFS Performance Report ===")
  table.insert(report, "Uptime: " .. string.format("%.1f", metrics.uptime) .. " seconds")
  table.insert(report, "")

  table.insert(report, "Sound Management:")
  table.insert(report, "  Created: " .. formatNumber(metrics.basic.SoundsCreated))
  table.insert(report, "  Destroyed: " .. formatNumber(metrics.basic.SoundsDestroyed))
  table.insert(
    report,
    "  Pool Efficiency: " .. string.format("%.1f%%", metrics.soundPoolEfficiency)
  )
  table.insert(report, "")

  table.insert(report, "System Performance:")
  table.insert(report, "  Footprints Created: " .. formatNumber(metrics.basic.FootprintsCreated))
  table.insert(
    report,
    "  Material Resolutions: " .. formatNumber(metrics.basic.MaterialResolutions)
  )
  table.insert(
    report,
    "  Average Resolution Time: " .. string.format("%.2f", metrics.averageResolutionTime) .. "ms"
  )
  table.insert(report, "  Network Requests: " .. formatNumber(metrics.basic.NetworkRequests))
  table.insert(report, "")

  table.insert(report, "Error Tracking:")
  table.insert(report, "  Total Errors: " .. formatNumber(metrics.basic.ErrorCount))
  table.insert(report, "  Error Rate: " .. string.format("%.2f%%", metrics.errorRate * 100))

  if next(metrics.errorsByType) then
    table.insert(report, "  Errors by Type:")
    for errorType, count in pairs(metrics.errorsByType) do
      table.insert(report, "    " .. errorType .. ": " .. formatNumber(count))
    end
  end

  return table.concat(report, "\n")
end

-- Reset metrics
function PerformanceMonitor:reset(): ()
  if not self._enabled then
    return
  end

  self._metrics.basic = {
    SoundsCreated = 0,
    SoundsDestroyed = 0,
    FootprintsCreated = 0,
    MaterialResolutions = 0,
    NetworkRequests = 0,
    ErrorCount = 0,
    LastReset = os.clock(),
  }

  self._metrics.soundPoolEfficiency = 0
  self._metrics.averageResolutionTime = 0
  self._metrics.errorRate = 0
  self._metrics.materialUsageByType = {}
  self._metrics.errorsByType = {}
  self._metrics.recentResolutionTimes = {}

  Utils.log("Performance metrics reset", "INFO")
end

-- Destroy monitor and cleanup
function PerformanceMonitor:destroy(): ()
  if self._enabled then
    Utils.log("Performance Monitor destroyed", "INFO")
  end

  -- Disconnect all connections
  for _, connection in self._connections do
    connection:Disconnect()
  end
  table.clear(self._connections)

  -- Clear metrics
  table.clear(self._metrics)

  self._enabled = false
end

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================

-- Initialize monitoring connections
function PerformanceMonitor:_initializeMonitoring(): ()
  -- Set up periodic metrics updates
  local connection = RunService.Heartbeat:Connect(function()
    self:_updateMetrics()
  end)
  table.insert(self._connections, connection)
end

-- Update calculated metrics
function PerformanceMonitor:_updateMetrics(): ()
  -- Update efficiency
  self:_updateEfficiency()

  -- Update error rate
  self:_updateErrorRate()
end

-- Update sound pool efficiency
function PerformanceMonitor:_updateEfficiency(): ()
  self._metrics.soundPoolEfficiency =
    calculateEfficiency(self._metrics.basic.SoundsCreated, self._metrics.basic.SoundsDestroyed)
end

-- Update error rate
function PerformanceMonitor:_updateErrorRate(): ()
  local totalOperations = self._metrics.basic.SoundsCreated
    + self._metrics.basic.FootprintsCreated
    + self._metrics.basic.MaterialResolutions
    + self._metrics.basic.NetworkRequests

  if totalOperations > 0 then
    self._metrics.errorRate = self._metrics.basic.ErrorCount / totalOperations
  else
    self._metrics.errorRate = 0
  end
end

-- Check and emit warnings with cooldown
function PerformanceMonitor:_checkWarning(warningType: string, message: string): ()
  local now = os.clock()
  local lastWarning = self._lastWarningTime[warningType] or 0

  if now - lastWarning >= WARNING_COOLDOWN then
    self._lastWarningTime[warningType] = now
    Utils.warn("Performance Warning (" .. warningType .. "): " .. message)
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================

return PerformanceMonitor
