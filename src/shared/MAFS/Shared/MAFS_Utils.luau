--!strict

--[[
    - file: MAFS_UTILS.LUAU

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Utility functions for the MAFS (Material Audio Footstep System).
      - Provides logging, validation, and helper functions.
      - Designed to avoid cyclic dependencies with configuration modules.

    - dependencies:
      - MAFS_Types

    - notes:
      - This module must NOT require MAFS_Configuration to avoid cyclic dependencies.
      - All configuration-dependent utilities should be in MAFS_Configuration instead.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")
local Types = require(mainPath:WaitForChild("MAFS_Types"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local LOG_PREFIX = "[MAFS]"
local WARN_PREFIX = "[MAFS WARNING]"
local ERROR_PREFIX = "[MAFS ERROR]"
local DEBUG_PREFIX = "[MAFS DEBUG]"
local SOUND_PART_SIZE = Vector3.new(0.1, 0.1, 0.1)

-- ============================================================================
-- TYPES
-- ============================================================================
export type LogLevel = Types.LogLevel
export type MAFSUtils = Types.MAFSUtils

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Get current timestamp for logging
local function getCurrentTimestamp(): string
  return os.date("%H:%M:%S", os.time())
end

-- Format log message with timestamp
local function formatLogMessage(prefix: string, message: string): string
  return string.format("%s [%s] %s", prefix, getCurrentTimestamp(), message)
end

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Utils = {}

-- Enhanced logging with levels
function Utils.log(message: string, level: LogLevel?): ()
  level = level or "INFO"

  if level == "DEBUG" then
    print(formatLogMessage(DEBUG_PREFIX, message))
  elseif level == "INFO" then
    print(formatLogMessage(LOG_PREFIX, message))
  elseif level == "WARN" then
    warn(formatLogMessage(WARN_PREFIX, message))
  elseif level == "ERROR" then
    error(formatLogMessage(ERROR_PREFIX, message))
  else
    print(formatLogMessage(LOG_PREFIX, message))
  end
end

-- Warning logging
function Utils.warn(message: string): ()
  warn(formatLogMessage(WARN_PREFIX, message))
end

-- Error logging
function Utils.error(message: string): ()
  error(formatLogMessage(ERROR_PREFIX, message))
end

-- Debug logging
function Utils.debug(message: string): ()
  print(formatLogMessage(DEBUG_PREFIX, message))
end

-- Format time from timestamp
function Utils.formatTime(timestamp: number): string
  return os.date("%H:%M:%S", timestamp)
end

-- Clamp number between min and max
function Utils.clampNumber(value: number, min: number, max: number): number
  return math.clamp(value, min, max)
end

-- Validate position vector
function Utils.isValidPosition(position: Vector3): boolean
  -- Check for NaN or infinite values
  if position.X ~= position.X or position.Y ~= position.Y or position.Z ~= position.Z then
    return false
  end

  if
    math.abs(position.X) == math.huge
    or math.abs(position.Y) == math.huge
    or math.abs(position.Z) == math.huge
  then
    return false
  end

  return true
end

-- Create standardized sound part
function Utils.createSoundPart(position: Vector3, name: string?): Part
  if not Utils.isValidPosition(position) then
    Utils.warn("Invalid position provided to createSoundPart, using origin")
    position = Vector3.new(0, 0, 0)
  end

  local part = Instance.new("Part")
  part.Name = name or "MAFSSoundEmitter"
  part.Anchored = true
  part.CanCollide = false
  part.Transparency = 1
  part.Size = SOUND_PART_SIZE
  part.CFrame = CFrame.new(position)

  return part
end

-- ============================================================================
-- ADDITIONAL UTILITY FUNCTIONS
-- ============================================================================

-- Safely get child with timeout
function Utils.safeWaitForChild(parent: Instance, childName: string, timeout: number?): Instance?
  timeout = timeout or 5

  local child = parent:WaitForChild(childName, timeout)
  if not child then
    Utils.warn(
      `Failed to find child '{childName}' in {parent:GetFullName()} after {timeout} seconds`
    )
  end

  return child
end

-- Check if instance is valid and not destroyed
function Utils.isValidInstance(instance: Instance?): boolean
  return instance ~= nil and instance.Parent ~= nil
end

-- Safe instance cleanup
function Utils.safeDestroy(instance: Instance?): ()
  if Utils.isValidInstance(instance) then
    instance:Destroy()
  end
end

-- Get distance between two positions
function Utils.getDistance(pos1: Vector3, pos2: Vector3): number
  return (pos1 - pos2).Magnitude
end

-- Check if position is within range
function Utils.isWithinRange(pos1: Vector3, pos2: Vector3, range: number): boolean
  return Utils.getDistance(pos1, pos2) <= range
end

-- Lerp between two numbers
function Utils.lerp(a: number, b: number, t: number): number
  return a + (b - a) * Utils.clampNumber(t, 0, 1)
end

-- Round number to specified decimal places
function Utils.round(number: number, decimals: number?): number
  decimals = decimals or 0
  local multiplier = 10 ^ decimals
  return math.floor(number * multiplier + 0.5) / multiplier
end

-- Convert seconds to human-readable time
function Utils.formatDuration(seconds: number): string
  if seconds < 60 then
    return string.format("%.1fs", seconds)
  elseif seconds < 3600 then
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    return string.format("%dm %.1fs", minutes, remainingSeconds)
  else
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    return string.format("%dh %dm", hours, minutes)
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================

return Utils
