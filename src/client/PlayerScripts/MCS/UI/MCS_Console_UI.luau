--!strict

--[[
    - file: MCS_Console_UI.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Provides command input functionality for the Modular Command System (MCS)
      - Modern terminal-inspired UI with consistent styling
      - Supports command execution, autocompletion, and command history navigation
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ContextActionService = game:GetService("ContextActionService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- ============================================================================
-- MODULES
-- ============================================================================
local AutocompleteService =
  require(script.Parent.Parent.Core:WaitForChild("MCS_AutocompleteService"))
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- UI fallback values
local UI_DEFAULTS = {
  BACKGROUND = Color3.fromRGB(30, 30, 35),
  BORDER = Color3.fromRGB(240, 240, 230),
  SUCCESS_TEXT = Color3.fromRGB(80, 250, 123),
  ERROR_TEXT = Color3.fromRGB(255, 85, 85),
  TEXT = Color3.fromRGB(248, 248, 242),
  CORNER_RADIUS = UDim.new(0, 6),
  ANIMATION_TIME = 0.3,
  MAX_SUGGESTION_HEIGHT = 150,
  SUGGESTION_BUTTON_HEIGHT = 28,
  SUGGESTION_PADDING = 2,
} :: Types.UI_ConstantsType

local UI_THEME: Types.UI_ConstantsType = Configuration.UI or UI_DEFAULTS

-- Animation constants
local ANIMATION_TIME = UI_THEME.ANIMATION_TIME
local MAX_SUGGESTION_HEIGHT = UI_THEME.MAX_SUGGESTION_HEIGHT
local SUGGESTION_BUTTON_HEIGHT = UI_THEME.SUGGESTION_BUTTON_HEIGHT
local SUGGESTION_PADDING = UI_THEME.SUGGESTION_PADDING

-- ============================================================================
-- VARIABLES
-- ============================================================================
-- Player references
local player: Player = Players.LocalPlayer or Players:WaitForChild("LocalPlayer") :: Player
local playerGui: PlayerGui = player:WaitForChild("PlayerGui") :: PlayerGui

-- Command History data store
local commandHistory: { string } = {}
local historyIndex = 0
local currentInput = ""

-- UI references with type assertions
local uiConsole = playerGui:WaitForChild("MCS_Console") :: ScreenGui
local uiMainFrame = uiConsole:WaitForChild("MainContainer") :: Frame
local uiHeaderBar = uiMainFrame:WaitForChild("HeaderBar") :: Frame
local uiCloseButton = uiHeaderBar:WaitForChild("CloseButton") :: TextButton
local uiTextBox = uiMainFrame:WaitForChild("CommandInput") :: TextBox
local uiConsoleFrame = uiMainFrame:WaitForChild("ConsoleFrame") :: Frame
local uiSuggestions = uiMainFrame:WaitForChild("Suggestions") :: ScrollingFrame

-- Remote references (only for console permission check)
local MCS_Remotes = ReplicatedStorage.MCS:WaitForChild("Remotes")
local CheckConsolePermission = MCS_Remotes:WaitForChild("CheckConsolePermission") :: RemoteFunction

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Console = {}

-- Event for when a command is submitted
Console.onCommandSubmitted = Instance.new("BindableEvent")

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Console Initialization
function Console.init()
  Utils.print("Console", "Initializing...")

  -- Check console permission
  local hasPermission = CheckConsolePermission:InvokeServer()
  if not hasPermission then
    Utils.print("Console", "Player lacks permission to use console")
    uiConsole.Enabled = false
    return
  end

  -- Connect close button
  uiCloseButton.MouseButton1Click:Connect(function()
    Console.hide()
    Utils.print("Console", "Close button clicked, closing console")
  end)

  -- Bind "F2" to toggle console
  ContextActionService:BindActionAtPriority(
    "ToggleConsole",
    function(_actionName, inputState, _inputObject)
      if inputState == Enum.UserInputState.Begin then
        Console.toggle()
        Utils.print("Console", "Console toggled via F2")
        return Enum.ContextActionResult.Sink
      end
      return Enum.ContextActionResult.Pass
    end,
    false,
    3000,
    Enum.KeyCode.F2
  )

  -- Submit command on Enter
  uiTextBox.FocusLost:Connect(function(enterPressed)
    if enterPressed then
      local commandText = Utils.sanitize(uiTextBox.Text)
      if commandText ~= "" then
        -- Add to command history
        table.insert(commandHistory, 1, commandText)
        if #commandHistory > Configuration.Commands.MAX_SUGGESTIONS or 20 then
          table.remove(commandHistory)
        end
        historyIndex = 0
        Utils.print("Console", "Command sent: " .. commandText)

        -- Process the command - add prefix if not present
        local finalCommandText = commandText
        if not Utils.hasPrefix(commandText, Configuration.Commands.PREFIX) then
          finalCommandText = Configuration.Commands.PREFIX .. commandText
        end

        -- Fire the command input event
        Console.onCommandSubmitted:Fire(finalCommandText)
      end
      uiTextBox.Text = ""
      Console.hide()
    end
  end)

  -- Update suggestions as player types
  uiTextBox:GetPropertyChangedSignal("Text"):Connect(function()
    Console.updateSuggestions(uiTextBox.Text)
  end)

  -- Command history navigation
  UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if not uiConsoleFrame.Visible or gameProcessed then
      return
    end

    if input.KeyCode == Enum.KeyCode.Up then
      Console.navigateHistory(1)
      Utils.print("Console", "Navigated history up")
    elseif input.KeyCode == Enum.KeyCode.Down then
      Console.navigateHistory(-1)
      Utils.print("Console", "Navigated history down")
    end
  end)
end

-- ============================================================================
-- UI FUNCTIONS
-- ============================================================================

-- Show Console
function Console.show()
  if not uiMainFrame then
    Utils.print("Console", "MainContainer not found")
    return
  end

  -- Reset for new session
  uiTextBox.Text = ""
  uiSuggestions.Visible = false

  -- Prepare animation
  uiMainFrame.Position = UDim2.new(0.5, -250, 0.2, -350)
  uiMainFrame.Visible = true

  -- Animate in
  local tween = TweenService:Create(
    uiMainFrame,
    TweenInfo.new(ANIMATION_TIME, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
    { Position = UDim2.new(0.5, -250, 0.3, -150) }
  )
  tween:Play()

  -- Focus the uiTextBox
  uiTextBox:CaptureFocus()

  -- Bind Escape to hide console
  ContextActionService:BindAction("HideConsole", function(_actionName, inputState, _inputObject)
    if inputState == Enum.UserInputState.Begin then
      Console.hide()
      Utils.print("Console", "Console hidden via Escape")
      return Enum.ContextActionResult.Sink
    end
    return Enum.ContextActionResult.Pass
  end, false, Enum.KeyCode.Escape)
end

-- Hide Console
function Console.hide()
  if not uiMainFrame or not uiMainFrame.Visible then
    return
  end

  -- Animate out
  local tween = TweenService:Create(
    uiMainFrame,
    TweenInfo.new(ANIMATION_TIME * 0.7, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
    { Position = UDim2.new(0.5, -250, 0.2, -350) }
  )

  tween.Completed:Connect(function()
    uiMainFrame.Visible = false
  end)

  tween:Play()

  -- Unbind Escape action
  ContextActionService:UnbindAction("HideConsole")
end

-- Toggle Console (show/hide)
function Console.toggle()
  if uiMainFrame and uiMainFrame.Visible then
    Console.hide()
  else
    Console.show()
  end
end

-- Update suggestions
function Console.updateSuggestions(partialText: string)
  -- Get suggestions from the AutocompleteService
  local suggestionStrings: { string } = AutocompleteService.getSuggestions(partialText)

  -- Convert string suggestions to AvailableCommand format for compatibility
  local suggestions: { Types.AvailableCommand } = {}
  for _, suggestionStr in ipairs(suggestionStrings) do
    table.insert(suggestions, {
      name = suggestionStr,
      description = "Command suggestion",
      usage = suggestionStr,
      aliases = {},
    })
  end

  -- Clear existing suggestions
  for _, child in ipairs(uiSuggestions:GetChildren()) do
    if child:IsA("TextButton") then
      child:Destroy()
    end
  end

  -- Update suggestions list
  local totalHeight = 0
  for i, suggestion: Types.AvailableCommand in ipairs(suggestions) do
    local isPlayerSuggestion = suggestion.name:match("^ban%s+%w+$")
      or suggestion.name:match("^kick%s+%w+$")
    local button = Instance.new("TextButton")
    button.Name = "Suggestion_" .. i
    button.Size = UDim2.new(1, 0, 0, SUGGESTION_BUTTON_HEIGHT)
    button.BackgroundTransparency = 0.9
    button.BackgroundColor3 = UI_THEME.BACKGROUND
    button.TextSize = 12
    button.TextColor3 = isPlayerSuggestion and UI_THEME.SUCCESS_TEXT or UI_THEME.TEXT
    button.TextXAlignment = Enum.TextXAlignment.Left
    button.BorderSizePixel = 0
    button.LayoutOrder = i

    -- Add icon based on suggestion type
    local icon = isPlayerSuggestion and "👤 " or "⚙️ "
    button.Text = icon .. suggestion.name .. " - " .. suggestion.description

    local buttonCorner = Instance.new("UICorner")
    buttonCorner.CornerRadius = UI_THEME.CORNER_RADIUS
    buttonCorner.Parent = button

    button.MouseEnter:Connect(function()
      button.BackgroundTransparency = 0.7
      button.TextColor3 = UI_THEME.SUCCESS_TEXT
    end)

    button.MouseLeave:Connect(function()
      button.BackgroundTransparency = 0.9
      button.TextColor3 = isPlayerSuggestion and UI_THEME.SUCCESS_TEXT or UI_THEME.TEXT
    end)

    button.MouseButton1Click:Connect(function()
      local currentText = uiTextBox.Text
      local commandParts = Utils.splitCommandText(currentText)
      if #commandParts > 0 then
        commandParts[1] = suggestion.name
        local newCommand = table.concat(commandParts, " ")
        uiTextBox.Text = newCommand
        uiTextBox.CursorPosition = #newCommand + 1
      else
        uiTextBox.Text = suggestion.name
        uiTextBox.CursorPosition = #suggestion.name + 1
      end
      uiSuggestions.Visible = false
    end)

    button.Parent = uiSuggestions
    totalHeight = totalHeight + SUGGESTION_BUTTON_HEIGHT + SUGGESTION_PADDING

    -- Limit number of visible suggestions
    if i >= Configuration.Commands.MAX_SUGGESTIONS then
      break
    end
  end

  -- Update suggestion frame size and visibility
  if #suggestions > 0 then
    local targetHeight = math.min(MAX_SUGGESTION_HEIGHT, totalHeight)

    -- Animate suggestion panel
    uiSuggestions.Visible = true
    TweenService
      :Create(uiSuggestions, TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
        Size = UDim2.new(1, -20, 0, targetHeight),
        Position = UDim2.new(0, 10, 1, -60 - targetHeight),
      })
      :Play()

    uiSuggestions.CanvasSize = UDim2.new(0, 0, 0, totalHeight)
  else
    uiSuggestions.Visible = false
  end
end

-- History Navigation
function Console.navigateHistory(direction: number)
  -- Save current input if we're starting navigation
  if historyIndex == 0 and direction > 0 then
    currentInput = uiTextBox.Text
  end

  -- Calculate new index
  local newIndex = historyIndex + direction

  -- Bounds check
  if newIndex < 0 then
    newIndex = 0
  elseif newIndex > #commandHistory then
    newIndex = #commandHistory
  end

  -- Update text based on navigation
  if newIndex == 0 then
    uiTextBox.Text = currentInput
  elseif commandHistory[newIndex] then
    uiTextBox.Text = commandHistory[newIndex]
  end

  -- Update cursor position to end
  uiTextBox.CursorPosition = #uiTextBox.Text + 1

  -- Update history index
  historyIndex = newIndex
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Console
