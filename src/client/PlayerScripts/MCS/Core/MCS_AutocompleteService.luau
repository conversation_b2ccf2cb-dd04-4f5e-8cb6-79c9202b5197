--!strict

--[[
    - file: MCS_AutocompleteService.luau

    - version: 2.0.1
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side autocomplete service for the Modular Command System (MCS)
      - Provides command and player name suggestions via server queries
      - Integrates with MCS_CommandParser and MCS_CommandDispatcher
      - Caches server responses for performance
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local CommandParser = require(script.Parent:WaitForChild("MCS_CommandParser"))
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local AutocompleteService = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local autocompleteRemote: RemoteFunction
local suggestionCache: { Types.AvailableCommand } = {}
local lastCacheUpdate: number = 0
local CACHE_DURATION: number = Configuration.Settings.AutocompleteCacheDuration or 60
local MODULE_NAME = "MCS_AutocompleteService" :: string

-- Player list cache for performance
local playerListCache: { Player } = {}
local lastPlayerCacheUpdate: number = 0
local PLAYER_CACHE_DURATION: number = 5 -- 5 seconds

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Get cached player list for performance
local function getCachedPlayers(): { Player }
  local currentTime = os.time()
  if currentTime - lastPlayerCacheUpdate > PLAYER_CACHE_DURATION then
    playerListCache = Players:GetPlayers()
    lastPlayerCacheUpdate = currentTime
  end
  return playerListCache
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize the service with a remote function
function AutocompleteService.init(remote: RemoteFunction)
  Utils.print(MODULE_NAME, "Initializing service")
  autocompleteRemote = remote

  -- Clear cache
  suggestionCache = {}
  lastCacheUpdate = 0

  Utils.print(MODULE_NAME, "Service initialized successfully")
end

-- ============================================================================
-- PUBLIC FUNCTIONS
-- ============================================================================

-- Get suggestions for the current command input
function AutocompleteService.getSuggestions(text: string): { string }
  Utils.print(MODULE_NAME, "Getting suggestions for: " .. text)
  Utils.startTimer("getSuggestions")

  if not autocompleteRemote then
    Utils.print(MODULE_NAME, "Remote not initialized")
    Utils.endTimer("AutocompleteService", "getSuggestions")
    return {} :: { string }
  end

  -- Ensure text is always a string
  local sanitizedText: string = if type(text) == "string"
    then Utils.sanitize(text) :: string or ""
    else ""

  -- Handle empty or prefix-only input
  if sanitizedText == Configuration.Commands.PREFIX or sanitizedText == "" then
    local suggestions: { Types.AvailableCommand } = AutocompleteService.getAvailableCommands()
    local result: { string } = {}
    for _, command in ipairs(suggestions) do
      table.insert(result, Configuration.Commands.PREFIX .. command.name)
    end
    Utils.print(MODULE_NAME, "Returning " .. #result .. " suggestions for prefix")
    Utils.endTimer("AutocompleteService", "getSuggestions")

    -- Explicitly return a table
    local maxSuggestions: number = Configuration.Commands.MAX_SUGGESTIONS or 5
    if #result > maxSuggestions then
      return { table.unpack(result, 1, maxSuggestions) }
    end
    return result
  end

  -- Check if it's a command or could be a partial command for autocomplete
  local isFullCommand = CommandParser.isCommand(sanitizedText)
  local tokens: { string } = {}

  if isFullCommand then
    -- Full command with prefix - strip prefix and tokenize
    tokens =
      Utils.splitCommandText(Utils.stripPrefix(sanitizedText, Configuration.Commands.PREFIX) or "")
  else
    -- Partial command without prefix - tokenize directly for autocomplete
    tokens = Utils.splitCommandText(sanitizedText)
    if #tokens == 0 then
      Utils.print(MODULE_NAME, "Not a command")
      Utils.endTimer("AutocompleteService", "getSuggestions")
      return {} :: { string }
    end
  end

  local shouldUseCache = #tokens <= 1 -- Only use cache for command suggestions, not player names

  -- Check cache (only for command suggestions, not player names)
  local currentTime: number = os.time()
  if
    shouldUseCache
    and #suggestionCache > 0
    and (currentTime - lastCacheUpdate) < CACHE_DURATION
  then
    local suggestions: { string } =
      AutocompleteService.convertCommandsToSuggestions(suggestionCache, sanitizedText)
    Utils.print(MODULE_NAME, "Returning " .. #suggestions .. " cached suggestions")
    Utils.endTimer("AutocompleteService", "getSuggestions")
    return suggestions
  end

  -- Query server for suggestions
  Utils.print(MODULE_NAME, "Querying server for suggestions: " .. sanitizedText)
  local success: boolean, response: any = pcall(function()
    return autocompleteRemote:InvokeServer(sanitizedText)
  end)

  Utils.print(MODULE_NAME, "Server query success: " .. tostring(success))
  if success then
    Utils.print(MODULE_NAME, "Response type: " .. typeof(response))
  else
    Utils.print(MODULE_NAME, "Server query error: " .. tostring(response))
  end

  if success and type(response) == "table" then
    local queryResponse: Types.QueryResponse = response :: Types.QueryResponse
    if queryResponse.success and queryResponse.data then
      -- Only cache command suggestions, not player name suggestions
      if shouldUseCache then
        suggestionCache = queryResponse.data
        lastCacheUpdate = currentTime
      end
      Utils.print(MODULE_NAME, "Fetched " .. #queryResponse.data .. " suggestions from server")

      local suggestions: { string } =
        AutocompleteService.convertCommandsToSuggestions(queryResponse.data, sanitizedText)
      Utils.endTimer("AutocompleteService", "getSuggestions")
      return suggestions
    else
      Utils.print(
        MODULE_NAME,
        "Server returned failed response: " .. (queryResponse.message or "Unknown")
      )
      Utils.endTimer("AutocompleteService", "getSuggestions")
      return {} :: { string }
    end
  else
    Utils.print(MODULE_NAME, "Failed to fetch suggestions from server: " .. tostring(response))
    Utils.endTimer("AutocompleteService", "getSuggestions")
    return {} :: { string }
  end
end

-- Convert AvailableCommand objects to string suggestions
function AutocompleteService.convertCommandsToSuggestions(
  commands: { Types.AvailableCommand },
  partialText: string
): { string }
  local suggestions: { string } = {}
  local prefix: string = Configuration.Commands.PREFIX
  local hasPrefix: boolean = Utils.hasPrefix(partialText, prefix)

  -- Handle both prefixed and non-prefixed input
  local searchText: string = if hasPrefix
    then Utils.stripPrefix(partialText, prefix) :: string or ""
    else partialText
  local tokens: { string } = Utils.splitCommandText(searchText) or {}
  local searchLower: string = searchText:lower()
  local commandName: string = tokens[1] and tokens[1]:lower() or ""

  -- Command name suggestions
  if #tokens == 1 then
    for _, command in ipairs(commands) do
      if command.name:lower():sub(1, #searchLower) == searchLower then
        table.insert(suggestions, prefix .. command.name)
      end
      for _, alias in ipairs(command.aliases) do
        if alias:lower():sub(1, #searchLower) == searchLower then
          -- Check if alias already has prefix to avoid double prefix
          local suggestion = if Utils.hasPrefix(alias, prefix) then alias else prefix .. alias
          table.insert(suggestions, suggestion)
        end
      end
    end
  end

  -- Player name suggestions for commands that accept player arguments
  if #tokens >= 2 then
    -- Check if this command is available and likely accepts player arguments
    local isPlayerCommand = false
    for _, command in ipairs(commands) do
      if command.name:lower() == commandName then
        -- Check if the command usage suggests it takes player arguments
        local usage = command.usage:lower()
        if usage:find("player") or usage:find("<player>") or usage:find("[player]") then
          isPlayerCommand = true
          break
        end
      end
    end

    -- Also check centralized configuration
    local playerCommands = Configuration.Commands.PLAYER_ARGUMENT_COMMANDS
    if playerCommands and type(playerCommands) == "table" then
      for _, cmd in ipairs(playerCommands) do
        if commandName == cmd then
          isPlayerCommand = true
          break
        end
      end
    end

    if isPlayerCommand then
      local partialName: string = tokens[2]:lower()

      for _, player: Player in ipairs(getCachedPlayers()) do
        if Utils.playerNameMatches(player, partialName) then
          local suggestion: string = prefix .. commandName .. " " .. player.Name
          table.insert(suggestions, suggestion)
        end
      end
    end
  end

  -- Remove duplicates
  local uniqueSuggestions: { string } = {}
  local seen: { [string]: boolean } = {}
  for _, suggestion: string in ipairs(suggestions) do
    if not seen[suggestion] then
      table.insert(uniqueSuggestions, suggestion)
      seen[suggestion] = true
    end
  end

  -- Return limited suggestions
  local maxSuggestions: number = Configuration.Commands.MAX_SUGGESTIONS or 5
  if #uniqueSuggestions > maxSuggestions then
    return { table.unpack(uniqueSuggestions, 1, maxSuggestions) }
  end
  return uniqueSuggestions
end

-- Get available commands for the player
function AutocompleteService.getAvailableCommands(): { Types.AvailableCommand }
  Utils.print(MODULE_NAME, "Getting available commands")
  Utils.startTimer("getAvailableCommands")

  if not autocompleteRemote then
    Utils.print(MODULE_NAME, "Remote not initialized")
    Utils.endTimer("AutocompleteService", "getAvailableCommands")
    return {} :: { Types.AvailableCommand }
  end

  -- Use empty input to get all accessible commands
  local success: boolean, response: any = pcall(function()
    return autocompleteRemote:InvokeServer(Configuration.Commands.PREFIX)
  end)

  if success and type(response) == "table" then
    local queryResponse: Types.QueryResponse = response :: Types.QueryResponse
    if queryResponse.success and queryResponse.data then
      Utils.print(MODULE_NAME, "Fetched " .. #queryResponse.data .. " available commands")
      Utils.endTimer("AutocompleteService", "getAvailableCommands")
      return queryResponse.data
    else
      Utils.print(
        MODULE_NAME,
        "Server returned failed response: " .. (queryResponse.message or "Unknown")
      )
      Utils.endTimer("AutocompleteService", "getAvailableCommands")
      return {} :: { Types.AvailableCommand }
    end
  else
    Utils.print(MODULE_NAME, "Failed to fetch available commands: " .. tostring(response))
    Utils.endTimer("AutocompleteService", "getAvailableCommands")
    return {} :: { Types.AvailableCommand }
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
Utils.print(MODULE_NAME, "Module initialized")
return AutocompleteService
