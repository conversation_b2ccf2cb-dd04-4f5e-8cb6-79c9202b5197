--!strict

--[[
    - file: MAFS_CLIENT.LUAU

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side implementation for the MAFS (Material Audio Footstep System).
      - Handles footstep sound playback, footprint creation, and sound synchronization.
      - Integrates with performance monitoring and centralized configuration.

    - dependencies:
      - MAFS_Types
      - MAFS_Utils
      - MAFS_Remotes
      - MAFS_Configuration

    - notes:
      - Manages local footstep sounds and replicated 3D sounds for other players.
      - Uses centralized utilities and configuration for consistency.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Debris = game:GetService("Debris")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local mainPath = ReplicatedStorage:WaitForChild("MAFS")
local Config = require(
  ReplicatedStorage:WaitForChild("Configurations")
    :WaitForChild("Systems")
    :WaitForChild("MAFS_Configuration")
)
local Remotes = require(mainPath:WaitForChild("MAFS_Remotes"))
local Types = require(mainPath:WaitForChild("MAFS_Types"))
local Utils = require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local WORKSPACE_MAFS_PATH = "Game.MAFS"
local SOUNDS_FOLDER_NAME = "Sounds"
local FOOTPRINTS_FOLDER_NAME = "FootPrints"

-- ============================================================================
-- TYPES
-- ============================================================================
export type MAFSConfiguration = Types.MAFSConfiguration

-- ============================================================================
-- VARIABLES
-- ============================================================================
local currentFootstepSound: Sound? = nil
local soundPart: Part? = nil
local performanceMonitor: Types.PerformanceMonitor? = nil

-- Resource folders
local soundsFolder: Folder? = nil
local footprintsFolder: Folder? = nil

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Initialize resource folders
local function initializeResourceFolders(): ()
  local mafsFolder = Utils.safeWaitForChild(ReplicatedStorage, "MAFS")
  if not mafsFolder then
    Utils.error("Failed to find MAFS folder in ReplicatedStorage")
    return
  end

  soundsFolder = Utils.safeWaitForChild(mafsFolder, SOUNDS_FOLDER_NAME)
  footprintsFolder = Utils.safeWaitForChild(mafsFolder, FOOTPRINTS_FOLDER_NAME)

  if not soundsFolder then
    Utils.warn("Sounds folder not found - footstep sounds will not work")
  end

  if not footprintsFolder then
    Utils.warn("Footprints folder not found - footprints will not work")
  end
end

-- Get workspace MAFS folder
local function getWorkspaceMAFSFolder(): Folder?
  local gameFolder = workspace:FindFirstChild("Game")
  if not gameFolder then
    Utils.warn("Game folder not found in workspace")
    return nil
  end

  local mafsFolder = gameFolder:FindFirstChild("MAFS")
  if not mafsFolder then
    Utils.warn("MAFS folder not found in workspace.Game")
    return nil
  end

  return mafsFolder :: Folder
end

-- Clean up current footstep sound
local function cleanupSound(): ()
  if currentFootstepSound then
    currentFootstepSound:Stop()
    Utils.safeDestroy(currentFootstepSound)
    currentFootstepSound = nil

    if performanceMonitor then
      performanceMonitor:recordSoundDestruction()
    end
  end

  if soundPart then
    Utils.safeDestroy(soundPart)
    soundPart = nil
  end
end

-- Create footprint at position
local function placeFootprint(position: Vector3, footprintName: string): ()
  if not Config.Config.enableFootprints then
    return
  end

  if not footprintsFolder then
    Utils.warn("Footprints folder not available")
    return
  end

  local template = footprintsFolder:FindFirstChild(footprintName)
  if not template or not template:IsA("Model") then
    Utils.warn(`Invalid footprint template: {footprintName}`)
    return
  end

  if not template.PrimaryPart then
    Utils.warn(`No PrimaryPart in footprint model: {footprintName}`)
    return
  end

  local workspaceMAFS = getWorkspaceMAFSFolder()
  if not workspaceMAFS then
    return
  end

  local footprint = template:Clone()
  local offsetPosition = position + Config.Config.footprintOffset
  footprint:PivotTo(CFrame.new(offsetPosition))
  footprint.Parent = workspaceMAFS

  Debris:AddItem(footprint, Config.Config.footprintLifetime)

  if performanceMonitor then
    performanceMonitor:recordFootprintCreation()
  end

  Utils.log(`Placed footprint: {footprintName} at {offsetPosition}`, "DEBUG")
end

-- Create sound for footstep
local function createFootstepSound(soundName: string, isLooped: boolean): Sound?
  if not soundsFolder then
    Utils.warn("Sounds folder not available")
    return nil
  end

  local template = soundsFolder:FindFirstChild(soundName)
  if not template or not template:IsA("Sound") then
    Utils.warn(`Missing sound template: {soundName}`)
    return nil
  end

  local sound = template:Clone()
  sound.Looped = isLooped

  if performanceMonitor then
    performanceMonitor:recordSoundCreation()
  end

  return sound
end

-- ============================================================================
-- REMOTE EVENT HANDLERS
-- ============================================================================

-- Start looping sound
Remotes.StartFootstepSound.OnClientEvent:Connect(function(soundName: string)
  if not Config.Config.enableFootsteps then
    return
  end

  cleanupSound()

  currentFootstepSound = createFootstepSound(soundName, true)
  if not currentFootstepSound then
    return
  end

  local workspaceMAFS = getWorkspaceMAFSFolder()
  if not workspaceMAFS then
    return
  end

  soundPart = Utils.createSoundPart(Vector3.new(0, 0, 0), "FootstepSoundEmitter")
  soundPart.Parent = workspaceMAFS

  currentFootstepSound.Parent = soundPart
  currentFootstepSound:Play()

  Utils.log(`Started footstep sound: {soundName}`, "DEBUG")
end)

-- Update sound type mid-step
Remotes.UpdateFootstepSound.OnClientEvent:Connect(function(newSoundName: string)
  if not Config.Config.enableFootsteps then
    return
  end

  if not currentFootstepSound or not soundPart then
    return
  end

  local newSound = createFootstepSound(newSoundName, true)
  if not newSound then
    return
  end

  currentFootstepSound:Stop()
  Utils.safeDestroy(currentFootstepSound)

  currentFootstepSound = newSound
  currentFootstepSound.Parent = soundPart
  currentFootstepSound:Play()

  Utils.log(`Updated footstep sound to: {newSoundName}`, "DEBUG")
end)

-- Stop looping sound
Remotes.StopFootstepSound.OnClientEvent:Connect(function()
  cleanupSound()
  Utils.log("Stopped footstep sound", "DEBUG")
end)

-- 3D one-shot footstep sound for other clients
Remotes.PlayFootstep.OnClientEvent:Connect(function(position: Vector3, soundName: string)
  if not Config.Config.enableFootsteps then
    return
  end

  if not Utils.isValidPosition(position) then
    Utils.warn("Invalid position for 3D footstep sound")
    return
  end

  local sound = createFootstepSound(soundName, false)
  if not sound then
    return
  end

  local workspaceMAFS = getWorkspaceMAFSFolder()
  if not workspaceMAFS then
    return
  end

  local part = Utils.createSoundPart(position, "3DFootstepSound")
  part.Parent = workspaceMAFS

  sound.Parent = part
  sound:Play()

  Debris:AddItem(part, sound.TimeLength + 1)

  Utils.log(`Played 3D footstep sound: {soundName} at {position}`, "DEBUG")
end)

-- Replicated footprint creation
Remotes.ReplicateFootprint.OnClientEvent:Connect(
  function(characterName: string, position: Vector3, footprintName: string)
    placeFootprint(position, footprintName)
  end
)

-- ============================================================================
-- SOUND SYNCHRONIZATION
-- ============================================================================

-- Sync looped sound emitter to character position
RunService.RenderStepped:Connect(function()
  if not soundPart then
    return
  end

  local player = Players.LocalPlayer :: Player
  local character = player.Character
  local root = character and character:FindFirstChild("HumanoidRootPart") :: BasePart?

  if root and Utils.isValidPosition(root.Position) then
    soundPart.CFrame = root.CFrame
  end
end)

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize performance monitor if enabled
local function initializePerformanceMonitor(): ()
  if Config.isPerformanceMonitorEnabled() then
    local PerformanceMonitor =
      require(mainPath:WaitForChild("Shared"):WaitForChild("MAFS_PerformanceMonitor"))
    performanceMonitor = PerformanceMonitor.new(Config.Config)
    Utils.log("Performance monitor initialized on client", "INFO")
  else
    Utils.log("Performance monitor disabled on client", "DEBUG")
  end
end

-- Initialize client
local function initializeClient(): ()
  Utils.log("Initializing MAFS client...", "INFO")

  -- Initialize resource folders
  initializeResourceFolders()

  -- Initialize performance monitoring
  initializePerformanceMonitor()

  -- Wait for remotes to be ready
  if not Remotes.waitForAllRemotes() then
    Utils.error("Failed to initialize remotes")
    return
  end

  Utils.log("MAFS client initialized successfully", "INFO")
end

-- Start initialization
task.spawn(initializeClient)
