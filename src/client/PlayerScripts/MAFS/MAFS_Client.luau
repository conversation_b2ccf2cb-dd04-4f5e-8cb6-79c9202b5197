--!strict
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Debris = game:GetService("Debris")

local Remotes = require(ReplicatedStorage.MAFS.MAFS_Remotes)
local Config = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)

local Sounds = ReplicatedStorage:WaitForChild("MAFS"):WaitForChild("Sounds")
local Footprints = ReplicatedStorage:WaitForChild("MAFS"):WaitForChild("FootPrints")

local currentFootstepSound: Sound? = nil
local soundPart: Part? = nil

local function cleanupSound()
	if currentFootstepSound then
		currentFootstepSound:Stop()
		currentFootstepSound:Destroy()
		currentFootstepSound = nil
	end
	if soundPart then
		soundPart:Destroy()
		soundPart = nil
	end
end

local function PlaceFootprint(position: Vector3, footprintName: string)
	local template = Footprints:FindFirstChild(footprintName)
	if not template or not template:IsA("Model") then
		warn(`[MAFS_Client] Invalid footprint: {footprintName}`)
		return
	end

	if not template.PrimaryPart then
		warn(`[MAFS_Client] No PrimaryPart in footprint model: {footprintName}`)
		return
	end

	local footprint = template:Clone()
	footprint:PivotTo(CFrame.new(position - Vector3.new(0, 3, 0)))
	footprint.Parent = workspace.Game.MAFS
	Debris:AddItem(footprint, Config.FootprintLifetime)
end

-- Start looping sound
Remotes.StartFootstepSound.OnClientEvent:Connect(function(soundName: string)
	cleanupSound()

	local template = Sounds:FindFirstChild(soundName) :: Sound?
	if not template then
		warn(`[MAFS_Client] Missing sound: {soundName}`)
		return
	end

	soundPart = Instance.new("Part")
	soundPart.Name = "FootstepSoundEmitter"
	soundPart.Anchored = true
	soundPart.CanCollide = false
	soundPart.Transparency = 1
	soundPart.Size = Vector3.new(0.1, 0.1, 0.1)
	soundPart.Parent = workspace.Game.MAFS

	currentFootstepSound = template:Clone()
	currentFootstepSound.Looped = true
	currentFootstepSound.Parent = soundPart
	currentFootstepSound:Play()
end)

-- Update sound type mid-step
Remotes.UpdateFootstepSound.OnClientEvent:Connect(function(newSoundName: string)
	if not currentFootstepSound or not soundPart then return end

	local newTemplate = Sounds:FindFirstChild(newSoundName) :: Sound?
	if not newTemplate then
		warn(`[MAFS_Client] Missing updated sound: {newSoundName}`)
		return
	end

	currentFootstepSound:Stop()
	currentFootstepSound:Destroy()

	currentFootstepSound = newTemplate:Clone()
	currentFootstepSound.Looped = true
	currentFootstepSound.Parent = soundPart
	currentFootstepSound:Play()
end)

-- Stop looping sound
Remotes.StopFootstepSound.OnClientEvent:Connect(function()
	cleanupSound()
end)

-- 3D one-shot footstep sound for other clients
Remotes.PlayFootstep.OnClientEvent:Connect(function(position: Vector3, soundName: string)
	local template = Sounds:FindFirstChild(soundName) :: Sound?
	if not template then
		warn(`[MAFS_Client] Missing replicated 3D sound: {soundName}`)
		return
	end

	local part = Instance.new("Part")
	part.Name = "3DFootstepSound"
	part.Anchored = true
	part.CanCollide = false
	part.Transparency = 1
	part.Size = Vector3.new(0.1, 0.1, 0.1)
	part.CFrame = CFrame.new(position)
	part.Parent = workspace.Game.MAFS

	local sound = template:Clone()
	sound.Looped = false
	sound.Parent = part
	sound:Play()

	Debris:AddItem(part, sound.TimeLength + 1)
end)

-- Replicated footprint creation
Remotes.ReplicateFootprint.OnClientEvent:Connect(function(characterName: string, position: Vector3, footprintName: string)
	PlaceFootprint(position, footprintName)
end)

-- Sync looped sound emitter to character
RunService.RenderStepped:Connect(function()
	local player = Players.LocalPlayer :: Player
	local character = player.Character
	local root = character and character:FindFirstChild("HumanoidRootPart") :: BasePart?

	if soundPart and root then
		soundPart.CFrame = root.CFrame
	end
end)