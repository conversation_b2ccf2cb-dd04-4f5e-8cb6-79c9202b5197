--!strict

--[[
    - file: Ban.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Bans a player from the game for a specified duration
      - Supports persistent bans using DataStores
      - Supports private server-specific bans that don't affect public servers
      - Useful for moderation and player management
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local PrivateServerPermissions =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PrivateServerPermissions"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "Ban"
local BAN_DATASTORE_NAME = "MCS_Bans"
local PRIVATE_BAN_DATASTORE_NAME = "MCS_PrivateServerBans"

-- ============================================================================
-- VARIABLES
-- ============================================================================
local banStore: DataStore?
local privateBanStore: DataStore?
local isPrivateServer: boolean = game.PrivateServerId ~= ""

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local BanCommand = {}

-- ============================================================================
-- COMMAND METADATA
-- ============================================================================
BanCommand.Description = "Bans a player from the game (use [private] for private server bans)"
BanCommand.Usage = "ban [[private]] <player> [reason] [duration]"
BanCommand.PermissionLevel = PermissionService.Levels.SeniorModerators

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
local function initialize()
  local success: boolean, errorMsg: string? = pcall(function()
    banStore = DataStoreService:GetDataStore(BAN_DATASTORE_NAME)
    if isPrivateServer then
      privateBanStore = DataStoreService:GetDataStore(PRIVATE_BAN_DATASTORE_NAME)
    end
  end)
  if not success then
    Utils.print(
      MODULE_NAME,
      string.format("Failed to initialize ban DataStores: %s", errorMsg or "unknown error")
    )
  end
end

initialize()

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================
local function isPlayerBanned(userId: number): (boolean, string?, number?)
  -- Check private server bans first if applicable
  if isPrivateServer and privateBanStore then
    local store: DataStore = privateBanStore
    local key = string.format("%s_%d", game.PrivateServerId, userId)
    local success, banData: Types.BanData? = pcall(function()
      return store:GetAsync(key)
    end)
    if success and banData then
      local banEnd = banData.endTime
      local reason = banData.reason
      if banEnd and os.time() < banEnd then
        return true, reason, banEnd
      end
    end
  end

  -- Check public server bans
  if banStore then
    local store: DataStore = banStore
    local success, banData: Types.BanData? = pcall(function()
      return store:GetAsync(tostring(userId))
    end)
    if success and banData then
      local banEnd = banData.endTime
      local reason = banData.reason
      if banEnd and os.time() < banEnd then
        return true, reason, banEnd
      end
    end
  end

  return false, nil, nil
end

local function applyBan(
  userId: number,
  reason: string,
  duration: number?,
  isPrivate: boolean
): (boolean, string?)
  local store: DataStore
  local key: string

  if isPrivate then
    if not isPrivateServer then
      return false, "Cannot apply private server ban in a public server"
    end
    if not privateBanStore then
      return false, "Private server ban DataStore not initialized"
    end
    store = privateBanStore
    key = string.format("%s_%d", game.PrivateServerId, userId)
  else
    if not banStore then
      return false, "Ban DataStore not initialized"
    end
    store = banStore
    key = tostring(userId)
  end

  local endTime = duration and (os.time() + duration) or math.huge
  local success: boolean, errorMsg: string? = pcall(function()
    store:SetAsync(
      key,
      {
        endTime = endTime,
        reason = reason,
      } :: Types.BanData
    )
  end)

  if not success then
    Utils.print(
      MODULE_NAME,
      string.format("Failed to save ban for %d: %s", userId, errorMsg or "unknown error")
    )
    return false, errorMsg or "unknown error"
  end

  Utils.print(
    MODULE_NAME,
    string.format(
      "Ban saved for UserId %d: %s (End: %s, Scope: %s)",
      userId,
      reason,
      endTime == math.huge and "Permanent" or os.date("%Y-%m-%d %H:%M:%S", endTime),
      isPrivate and "Private Server" or "Public Server"
    )
  )
  return true, nil
end

-- ============================================================================
-- EXECUTE FUNCTION
-- ============================================================================
function BanCommand.Execute(player: Player, args: { string }, prefix: string): Types.CommandResponse
  if #args < 1 then
    return {
      success = false,
      message = "Usage: " .. prefix .. "ban [[private]] <player> [reason] [duration]",
    }
  end

  -- Check for [private] flag
  local isPrivateBan = args[1]:lower() == "[private]"
  local targetNameIndex = isPrivateBan and 2 or 1

  -- Check if we have enough arguments
  if #args < targetNameIndex then
    return {
      success = false,
      message = "Usage: " .. prefix .. "ban [[private]] <player> [reason] [duration]",
    }
  end

  local targetName = args[targetNameIndex]

  -- Validate private server permissions if needed
  if isPrivateBan then
    if not isPrivateServer then
      return {
        success = false,
        message = "Private server bans can only be applied in a private server",
      }
    end
    local permissionLevel =
      PrivateServerPermissions.GetPlayerPermission(game.PrivateServerId, player.UserId)
    if not permissionLevel or permissionLevel < PermissionService.Levels.SeniorModerators then
      return { success = false, message = "Insufficient permissions to ban in this private server" }
    end
  end

  -- Find the target player
  local targetInstance
  for _, p in ipairs(Players:GetPlayers()) do
    if p.Name:lower() == targetName:lower() then
      targetInstance = p
      break
    end
  end

  if not targetInstance or not targetInstance:IsA("Player") then
    return {
      success = false,
      message = "Player '" .. targetName .. "' not found or is not a valid player",
    }
  end

  Utils.print("BanCommand", "Executing ban for: " .. targetInstance.Name)

  local target = targetInstance :: Player
  local targetUserId = target.UserId

  -- Check if already banned
  local isBanned, banReason, _ = isPlayerBanned(targetUserId)
  if isBanned then
    return {
      success = false,
      message = string.format("%s is already banned: %s", target.Name, banReason or "No reason"),
    }
  end

  -- Extract reason and duration
  local reasonIndex = targetNameIndex + 1
  local durationIndex = targetNameIndex + 2
  local reason = args[reasonIndex] or "No reason provided"
  local duration = Utils.parseDuration(args[durationIndex])

  -- Apply the ban
  local banSuccess, banError = applyBan(targetUserId, reason, duration, isPrivateBan)
  if not banSuccess then
    return { success = false, message = banError or "Failed to ban player" }
  end

  -- Kick the player
  local kickMessage = string.format(
    "Banned by %s: %s%s%s",
    player.Name :: string,
    tostring(reason :: string),
    duration and string.format(" (Duration: %s)", tostring(args[durationIndex] or "")) or "", -- Ensure args[durationIndex] is a string
    isPrivateBan and " (Private Server)" or ""
  )
  local kickSuccess = pcall(function()
    target:Kick(kickMessage)
  end)

  if not kickSuccess then
    Utils.print(MODULE_NAME, string.format("Failed to kick %s", target.Name))
  else
    Utils.print(MODULE_NAME, string.format("Kicked %s successfully", target.Name))
  end

  return {
    success = true,
    message = string.format(
      "Banned %s successfully%s",
      target.Name,
      isPrivateBan and " in this private server" or ""
    ),
  }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return BanCommand
